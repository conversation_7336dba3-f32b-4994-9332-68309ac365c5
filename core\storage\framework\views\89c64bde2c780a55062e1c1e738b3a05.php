<?php $__env->startSection('content'); ?>
    <div class="card custom--card">
        <div class="card-body">
            <div class="form-group mb-4">
                <label class="d-flex justify-content-between">
                    <span><?php echo app('translator')->get('Referral Link'); ?></span>
                    <?php if(auth()->user()->referrer): ?>
                        <span class="text--info"><?php echo app('translator')->get('You were referred by'); ?> <?php echo e(auth()->user()->referrer->fullname); ?></span>
                    <?php endif; ?>
                </label>
                <div class="input-group">
                    <input class="form-control form--control referralURL" name="text" type="text" value="<?php echo e(route('home')); ?>?reference=<?php echo e(auth()->user()->username); ?>" readonly="">
                    <button class="input-group-text copytext copyBoard" id="copyBoard"> <i class="fa fa-copy"></i> </button>
                </div>
            </div>
            
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card bg-dark">
                        <div class="card-body text-center">
                            <h4 class="text-white"><?php echo app('translator')->get('Total Earned'); ?></h4>
                            <h2 class="text-white mt-2"><?php echo e(showAmount($totalEarned)); ?></h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card bg-dark">
                        <div class="card-body text-center">
                            <h4 class="text-white"><?php echo app('translator')->get('Minimum Withdrawal'); ?></h4>
                            <h2 class="text-white mt-2"><?php echo e(showAmount($general->referral_min_withdrawal)); ?></h2>
                        </div>
                    </div>
                </div>
            </div>
            
            <?php if($user->allReferrals->count() > 0 && $maxLevel > 0): ?>
                <label><?php echo app('translator')->get('My Referrals'); ?></label>
                <div class="treeview-container">
                    <ul class="treeview">
                        <li class="items-expanded"> <?php echo e($user->fullname); ?> ( <?php echo e($user->username); ?> )
                            <?php echo $__env->make($activeTemplate . 'partials.under_tree', ['user' => $user, 'layer' => 0, 'isFirst' => true], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        </li>
                    </ul>
                </div>
            <?php endif; ?>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('style'); ?>
    <link type="text/css" href="<?php echo e(asset('assets/global/css/jquery.treeView.css')); ?>" rel="stylesheet">
<?php $__env->stopPush(); ?>
<?php $__env->startPush('script'); ?>
    <script src="<?php echo e(asset('assets/global/js/jquery.treeView.js')); ?>"></script>
    <script>
        (function($) {
            "use strict";
            $('.treeview').treeView();
            $('.copyBoard').click(function() {
                var copyText = document.getElementsByClassName("referralURL");
                copyText = copyText[0];
                copyText.select();
                copyText.setSelectionRange(0, 99999);
                document.execCommand("copy");
                iziToast.success({
                    message: "Copied: " + copyText.value,
                    position: "topRight"
                });
            });
        })(jQuery);
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make($activeTemplate . 'layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\crypto\core\resources\views/templates/dark/user/referral/index.blade.php ENDPATH**/ ?>