<?php $__env->startSection('content'); ?>

<?php if(!auth()->user()->ccl_token_address): ?>
<div class="alert alert-info mb-4" role="alert">
    <strong><?php echo app('translator')->get('Important:'); ?></strong> <?php echo app('translator')->get('Please provide your token address where you would like to receive your CCL tokens. If you do not submit this address now, you will NOT receive your tokens after the presale has ended.'); ?>
</div>

<div class="token-address-form mb-4">
    <form action="<?php echo e(route('user.ccl.token.save.token.address')); ?>" method="POST">
        <?php echo csrf_field(); ?>
        <div class="row">
            <div class="col-md-8">
                <div class="form-group">
                    <label for="token_address"><?php echo app('translator')->get('Your Token Address'); ?></label>
                    <input type="text" class="form-control" id="token_address" name="token_address" placeholder="Enter your token address" required>
                    <small class="text-muted"><?php echo app('translator')->get('This address will be used to receive your CCL tokens. Once submitted, it cannot be changed.'); ?></small>
                </div>
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn--base w-100"><?php echo app('translator')->get('Submit Address'); ?></button>
            </div>
        </div>
    </form>
</div>
<?php endif; ?>

<?php $__currentLoopData = $presales; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $presale): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
<div class="card custom--card mb-4">
    <div class="card-header">
        <h5 class="card-title"><?php echo e($presale->title); ?></h5>
    </div>
    <div class="card-body">
        <div class="gateway-card">
            <div class="row justify-content-center gy-sm-2 gy-2 deposit-row">
                <div class="col-lg-12">
                    <div class="token-sale-container">
                        <div class="token-sale-header mb-0">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="token-info-card compact-card">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <!-- Regular view (visible on larger screens) -->
                                                <div class="token-info-labels d-none d-md-block">
                                                    <div class="row">
                                                        <div class="col-2 text-center">
                                                            <p class="label-text mb-0"><?php echo app('translator')->get('Start Date'); ?></p>
                                                        </div>
                                                        <div class="col-2 text-center">
                                                            <p class="label-text mb-0"><?php echo app('translator')->get('End Date'); ?></p>
                                                        </div>
                                                        <div class="col-2 text-center">
                                                            <p class="label-text mb-0"><?php echo app('translator')->get('Quantity'); ?></p>
                                                        </div>
                                                        <div class="col-2 text-center">
                                                            <p class="label-text mb-0"><?php echo app('translator')->get('Price'); ?></p>
                                                        </div>
                                                        <div class="col-2 text-center">
                                                            <p class="label-text mb-0"><?php echo app('translator')->get('Next Price'); ?></p>
                                                        </div>
                                                        <div class="col-2 text-center">
                                                            <p class="label-text mb-0"><?php echo app('translator')->get('Status'); ?></p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="token-info-values mt-1 d-none d-md-block">
                                                    <div class="row">
                                                        <div class="col-2 text-center">
                                                            <p class="value-text mb-0"><?php echo e($presale->start_date ? $presale->start_date->format('F d, Y') : 'TBD'); ?></p>
                                                        </div>
                                                        <div class="col-2 text-center">
                                                            <p class="value-text mb-0"><?php echo e($presale->end_date ? $presale->end_date->format('F d, Y') : 'TBD'); ?></p>
                                                        </div>
                                                        <div class="col-2 text-center">
                                                            <p class="value-text mb-0"><?php echo e($presale->quantity ?? '0'); ?></p>
                                                        </div>
                                                        <div class="col-2 text-center">
                                                            <p class="value-text mb-0"><?php echo e(rtrim(rtrim(number_format($presale->price ?? 0, 8), '0'), '.')); ?> USD</p>
                                                        </div>
                                                        <div class="col-2 text-center">
                                                            <p class="value-text mb-0"><?php echo e($presale->next_price ? rtrim(rtrim(number_format($presale->next_price, 8), '0'), '.') . ' USD' : 'N/A'); ?></p>
                                                        </div>
                                                        <div class="col-2 text-center">
                                                            <span class="badge <?php echo e($presale->getStatus() == 'Active' ? 'badge--primary' : (($presale->getStatus() == 'Ended' || $presale->getStatus() == 'Completed') ? 'badge--success' : 'badge--warning')); ?>">
                                                                <?php echo e($presale->getStatus()); ?>

                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Mobile view (stacked) -->
                                                <div class="token-info-mobile d-md-none">
                                                    <div class="mobile-info-item">
                                                        <div class="mobile-info-label"><?php echo app('translator')->get('Start Date'); ?></div>
                                                        <div class="mobile-info-value"><?php echo e($presale->start_date ? $presale->start_date->format('F d, Y') : 'TBD'); ?></div>
                                                    </div>
                                                    <div class="mobile-info-item">
                                                        <div class="mobile-info-label"><?php echo app('translator')->get('End Date'); ?></div>
                                                        <div class="mobile-info-value"><?php echo e($presale->end_date ? $presale->end_date->format('F d, Y') : 'TBD'); ?></div>
                                                    </div>
                                                    <div class="mobile-info-item">
                                                        <div class="mobile-info-label"><?php echo app('translator')->get('Quantity'); ?></div>
                                                        <div class="mobile-info-value"><?php echo e($presale->quantity ?? '0'); ?></div>
                                                    </div>
                                                    <div class="mobile-info-item">
                                                        <div class="mobile-info-label"><?php echo app('translator')->get('Price'); ?></div>
                                                        <div class="mobile-info-value"><?php echo e(rtrim(rtrim(number_format($presale->price ?? 0, 8), '0'), '.')); ?> USD</div>
                                                    </div>
                                                    <div class="mobile-info-item">
                                                        <div class="mobile-info-label"><?php echo app('translator')->get('Next Price'); ?></div>
                                                        <div class="mobile-info-value"><?php echo e($presale->next_price ? rtrim(rtrim(number_format($presale->next_price, 8), '0'), '.') . ' USD' : 'N/A'); ?></div>
                                                    </div>
                                                    <div class="mobile-info-item">
                                                        <div class="mobile-info-label"><?php echo app('translator')->get('Status'); ?></div>
                                                        <div class="mobile-info-value">
                                                            <span class="badge <?php echo e($presale->getStatus() == 'Active' ? 'badge--primary' : (($presale->getStatus() == 'Ended' || $presale->getStatus() == 'Completed') ? 'badge--success' : 'badge--warning')); ?>">
                                                                <?php echo e($presale->getStatus()); ?>

                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="token-info-sold mt-1">
                                                    <div class="row">
                                                        <div class="col-md-12">
                                                            <div class="deposit-info compact-info mb-1">
                                                                <div class="deposit-info__title">
                                                                    <p class="text mb-0"><?php echo app('translator')->get('Progress'); ?></p>
                                                                </div>
                                                                <div class="deposit-info__input">
                                                                    <p class="text mb-0"><?php echo e(number_format($presale->getProgressPercentage(), 2)); ?>%</p>
                                                                </div>
                                                            </div>
                                                            <div class="progress">
                                                                <div class="progress-bar" role="progressbar" style="width: <?php echo e($presale->getProgressPercentage()); ?>%;" aria-valuenow="<?php echo e($presale->getProgressPercentage()); ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <?php if($presale->getStatus() == 'Active'): ?>
                                                <div class="mt-2 text-end">
                                                    <button type="button" class="btn btn--base buy-presale-btn" data-presale-id="<?php echo e($presale->id); ?>" data-token-symbol="<?php echo e($presale->token_symbol); ?>" data-token-price="<?php echo e($presale->price); ?>"><?php echo app('translator')->get('Buy Tokens'); ?></button>
                                                </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

<div class="crypto-disclaimer mt-4">
    <p class="disclaimer-text mb-0"><strong><?php echo app('translator')->get('Disclaimer:'); ?></strong> <?php echo app('translator')->get('Cryptocurrency investments are volatile and high risk. Prices can fluctuate significantly. We do not guarantee any returns and are not responsible for any losses incurred. Please invest only what you can afford to lose.'); ?></p>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('style'); ?>
<style>
    /* Custom card styles for first card */
    .card.custom--card:first-of-type .card-header {
        padding: 10px 20px;
    }

    .card.custom--card:first-of-type .card-body {
        padding: 5px;
    }

    /* Token address form styles */
    .token-address-form {
        background-color: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        margin-top: 0;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .token-address-form label {
        color: #7c97bb;
        font-size: 14px;
        margin-bottom: 8px;
        display: block;
    }

    .token-address-form .form-control {
        background-color: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: #fff;
        padding: 10px 15px;
        border-radius: 5px;
        height: auto;
    }

    .token-address-form .form-control:focus {
        background-color: rgba(255, 255, 255, 0.15);
        border-color: rgba(255, 255, 255, 0.3);
        box-shadow: 0 0 0 0.2rem rgba(190, 132, 0, 0.25);
    }

    .token-address-form .text-muted {
        color: #7c97bb !important;
        font-size: 12px;
        margin-top: 5px;
    }

    .alert-info {
        background-color: rgba(23, 162, 184, 0.2);
        border-color: rgba(23, 162, 184, 0.3);
        color: #d9edf7;
        margin-top: 0;
        padding: 12px 15px;
    }

    /* Custom card styles for presale cards */
    .card.custom--card:nth-of-type(1) .card-header {
        padding: 8px 20px;
    }

    .card.custom--card:nth-of-type(1) .card-body {
        padding: 3px;
    }

    /* Gateway card styles */
    .gateway-card {
        background-color: transparent;
        padding: 5px;
    }

    /* Compact gateway card for presale cards */
    .card.custom--card:nth-of-type(1) .gateway-card {
        padding: 3px;
    }

    /* Token info card styles */
    .token-info-card {
        background-color: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;
    }

    /* Mobile info styles */
    .token-info-mobile {
        padding: 5px;
    }

    .mobile-info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 5px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .mobile-info-item:last-child {
        border-bottom: none;
    }

    .mobile-info-label {
        color: #7c97bb;
        font-size: 13px;
        font-weight: 500;
    }

    .mobile-info-value {
        color: #fff;
        font-size: 14px;
        font-weight: 600;
        text-align: right;
    }

    /* Compact card styles */
    .compact-card {
        padding: 6px;
        margin-bottom: 3px;
    }

    /* Token info sold styles */
    .token-info-sold {
        margin-top: 3px !important;
    }

    .label-text {
        color: #7c97bb;
        font-size: 13px;
        font-weight: 500;
    }

    .value-text {
        color: #fff;
        font-size: 14px;
        font-weight: 600;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* Deposit info styles */
    .deposit-info {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        justify-content: space-between;
        margin-bottom: 12px;
    }

    /* Compact info styles */
    .compact-info {
        margin-bottom: 6px;
    }

    .deposit-info__title {
        max-width: 50%;
        margin-bottom: 0px;
        text-align: left;
    }

    .deposit-info__input {
        max-width: 50%;
        text-align: right;
        width: 100%;
    }

    .deposit-info__title .text {
        margin-bottom: 0;
    }

    .deposit-info__input .text {
        margin-bottom: 0;
    }

    /* Progress bar styles */
    .progress {
        height: 12px;
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 10px;
        overflow: hidden;
        box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
    }

    /* Progress bar for all cards should be consistent */
    .card.custom--card .progress {
        height: 12px;
        margin-bottom: 0;
    }

    .progress-bar {
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
        font-size: 10px;
        font-weight: 600;
        transition: width 0.6s ease;
        background-color: #BE8400;
        background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
        background-size: 16px 16px;
        animation: progress-bar-stripes 1s linear infinite;
    }

    @keyframes progress-bar-stripes {
        from { background-position: 16px 0; }
        to { background-position: 0 0; }
    }

    /* Badge styles */
    .badge--success {
        background-color: rgba(40, 199, 111, 0.2);
        border: 1px solid #28c76f;
        color: #28c76f;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
    }

    .badge--primary {
        background-color: rgba(0, 123, 255, 0.2);
        border: 1px solid #007bff;
        color: #007bff;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
    }

    .badge--warning {
        background-color: rgba(255, 193, 7, 0.2);
        border: 1px solid #ffc107;
        color: #ffc107;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
    }

    /* Amount input styles */
    .amount-input-group {
        max-width: 200px;
        margin-left: auto;
    }

    .input-group-text {
        background-color: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        color: #fff;
    }

    .form-control {
        background-color: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        color: #fff;
    }

    /* Compact form controls for buy tokens card */
    .card.custom--card:nth-of-type(2) .form-control,
    .card.custom--card:nth-of-type(2) .input-group-text {
        padding: 0.25rem 0.5rem;
        height: calc(1.5em + 0.5rem + 2px);
        font-size: 0.875rem;
    }

    .form-control:focus {
        background-color: rgba(255, 255, 255, 0.05);
        border-color: #BE8400;
        color: #fff;
        box-shadow: none;
    }

    /* Token purchase form inner container */
    .token-purchase-form-inner {
        width: 100%;
        max-width: 100%;
        margin: 0 auto;
    }

    /* Total amount styles */
    .total-amount {
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        padding-top: 8px;
        margin-top: 8px;
        font-weight: 700;
    }

    /* Button styles */
    .btn--base {
        background-color: #BE8400;
        border-color: #BE8400;
        color: #fff;
        padding: 6px 20px;
        border-radius: 5px;
    }

    .btn--base:hover {
        background-color: #9e6e00;
        border-color: #9e6e00;
        color: #fff;
    }

    /* Text danger */
    .text-danger {
        color: #dc3545 !important;
    }

    /* Presale Card Styles */
    .presale-card {
        background-color: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        overflow: hidden;
        transition: all 0.3s ease;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .presale-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    }

    .presale-card__header {
        padding: 15px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .presale-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 5px;
        color: #fff;
    }

    .token-info {
        font-size: 14px;
    }

    .token-name {
        color: #BE8400;
        font-weight: 500;
    }

    .token-symbol {
        color: #7c97bb;
    }

    .presale-card__body {
        padding: 15px;
        flex: 1;
    }

    .presale-info {
        margin-bottom: 15px;
    }

    .info-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
    }

    .info-label {
        color: #7c97bb;
        font-size: 13px;
    }

    .info-value {
        color: #fff;
        font-size: 13px;
        font-weight: 500;
    }

    .presale-progress {
        margin-top: 15px;
    }

    .progress-info {
        display: flex;
        justify-content: space-between;
        margin-bottom: 5px;
    }

    .progress-label {
        color: #7c97bb;
        font-size: 13px;
    }

    .progress-value {
        color: #fff;
        font-size: 13px;
        font-weight: 500;
    }

    .presale-card__footer {
        padding: 15px;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    /* Responsive styles */
    @media (max-width: 991px) {
        .deposit-info__title,
        .deposit-info__input {
            max-width: 100%;
            width: 100%;
            text-align: left;
        }
        .deposit-info__input {
            margin-top: 5px;
            text-align: left;
        }
        .deposit-info {
            flex-direction: column;
            align-items: flex-start;
        }
        .input-group, .amount-input-group {
            max-width: 100% !important;
            margin-left: 0;
        }
    }

    @media (max-width: 767px) {
        .gateway-card {
            padding: 5px;
        }
        .token-info-card {
            padding: 15px 10px;
        }
        .card-body {
            padding: 15px !important;
        }
        .label-text, .value-text {
            font-size: 13px;
        }
        .presale-card {
            margin-bottom: 20px;
        }

        /* Mobile optimizations for the token info display */
        .mobile-info-item {
            padding: 10px 5px;
        }
        .mobile-info-label {
            font-size: 13px;
            width: 40%;
        }
        .mobile-info-value {
            font-size: 13px;
            width: 60%;
        }

        /* Mobile optimizations for the buy form */
        .token-purchase-form-inner {
            padding: 0 5px;
        }
        .deposit-info {
            margin-bottom: 15px;
        }
        .deposit-info__title p.text {
            font-size: 14px;
        }
        .btn--base {
            width: 100%;
            margin-top: 10px;
        }
        .mt-2.text-end {
            text-align: center !important;
        }

        /* Progress bar adjustments for mobile - keeping consistent with desktop */
        .progress {
            height: 12px;
        }
        .token-info-sold {
            margin-top: 8px !important;
        }

        /* Crypto disclaimer mobile adjustments */
        .crypto-disclaimer {
            padding: 12px 15px;
            margin-top: 20px;
        }
        .disclaimer-text {
            font-size: 13px;
        }
    }

    /* Small mobile devices */
    @media (max-width: 480px) {
        .badge--success, .badge--primary, .badge--warning {
            padding: 4px 8px;
            font-size: 11px;
        }
        /* Further optimizations for small devices */
        .card-header h5.card-title {
            font-size: 16px;
        }
        .token-info-card {
            padding: 10px 5px;
        }
        .token-purchase-form-inner {
            padding: 0;
        }
        .deposit-info__title p.text {
            font-size: 13px;
        }
        .deposit-info {
            margin-bottom: 12px;
        }

        /* Smaller mobile info items */
        .mobile-info-item {
            padding: 8px 3px;
        }
        .mobile-info-label {
            font-size: 12px;
            width: 35%;
        }
        .mobile-info-value {
            font-size: 12px;
            width: 65%;
        }
    }

    /* Extra small mobile devices */
    @media (max-width: 375px) {
        .badge--success, .badge--primary, .badge--warning {
            padding: 3px 6px;
            font-size: 10px;
        }
        .card-header h5.card-title {
            font-size: 15px;
        }
        .deposit-info__title p.text {
            font-size: 12px;
        }

        /* Even smaller mobile info items */
        .mobile-info-item {
            padding: 7px 2px;
        }
        .mobile-info-label {
            font-size: 11px;
            width: 35%;
        }
        .mobile-info-value {
            font-size: 11px;
            width: 65%;
        }

        /* Tighter spacing for very small screens */
        .token-info-card {
            padding: 8px 3px;
        }
        .token-info-sold {
            margin-top: 5px !important;
        }
        .progress {
            height: 12px;
        }
    }

    /* Crypto disclaimer styles */
    .crypto-disclaimer {
        background-color: rgba(190, 132, 0, 0.1);
        border: 1px solid rgba(190, 132, 0, 0.3);
        border-radius: 5px;
        padding: 15px 20px;
        margin-bottom: 20px;
    }

    .disclaimer-text {
        color: #d9edf7;
        font-size: 14px;
        line-height: 1.5;
        text-align: center;
    }

    .disclaimer-text strong {
        color: #BE8400;
    }

    /* Very small mobile devices */
    @media (max-width: 320px) {
        .mobile-info-label {
            font-size: 10px;
        }
        .mobile-info-value {
            font-size: 10px;
        }
        .card-header h5.card-title {
            font-size: 14px;
        }
        .deposit-info__title p.text {
            font-size: 11px;
        }
        .disclaimer-text {
            font-size: 12px;
        }
    }

    /* Specific tablet device optimizations */
    @media only screen and (min-width: 768px) and (max-width: 1024px) {
        /* Optimize Buy Tokens card for tablets */
        .card.custom--card:nth-of-type(2) .card-body {
            padding: 15px 10px !important;
        }
        .token-purchase-form {
            max-width: 90%;
            margin: 0 auto;
        }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('script'); ?>
<script>
    (function ($) {
        "use strict";

        // Handle buy presale button click
        $('.buy-presale-btn').on('click', function() {
            // Get presale data from button attributes
            const presaleId = $(this).data('presale-id');
            const tokenSymbol = $(this).data('token-symbol');
            const tokenPrice = parseFloat($(this).data('token-price')) || 0;

            // Set the minimum amount
            const minAmount = 20;

            // Get user balance
            const userBalance = parseFloat('<?php echo e(auth()->user()->balance); ?>') || 0;

            // Check if user has enough balance
            if (userBalance < minAmount) {
                // User doesn't have enough balance, show insufficient funds modal
                const insufficientModal = $('#insufficientFundsModal');

                // Set the minimum required amount
                insufficientModal.find('.min-required-amount').text('$' + minAmount.toFixed(2));

                // Show the insufficient funds modal
                insufficientModal.modal('show');
                return;
            }

            // Calculate tokens based on minimum amount
            let tokens = 0;
            if (tokenPrice > 0) {
                tokens = minAmount / tokenPrice;
            }

            // Get the modal
            const modal = $('#buyPresaleModal');

            // Set hidden fields
            modal.find('#presale_id').val(presaleId);
            modal.find('#token_price').val(tokenPrice);

            // Set token symbol
            modal.find('.modal-token-symbol').text(tokenSymbol);

            // Set initial values
            modal.find('#modal_amount').val(minAmount);
            modal.find('#modal_tokens').val(tokens.toLocaleString('en-US', {maximumFractionDigits: 8}));
            modal.find('input[name=tokens]').val(tokens.toString());
            modal.find('.modal-final-amount').text('$' + minAmount.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ","));

            // Enable buy button if amount is valid
            if (minAmount >= 20) {
                modal.find('.modal-buy-btn').removeAttr('disabled');
            } else {
                modal.find('.modal-buy-btn').attr('disabled', true);
            }

            // Show the modal
            modal.modal('show');

            // Flag to prevent infinite loop between the two input handlers
            let isModalCalculating = false;

            // Calculate tokens based on amount
            modal.find('.modal-amount').on('input', function() {
                if (isModalCalculating) return;
                isModalCalculating = true;

                const amount = parseFloat($(this).val()) || 0;

                // Calculate tokens based on price
                let tokens = 0;
                if (tokenPrice > 0) {
                    tokens = amount / tokenPrice;
                }

                // Update tokens field with formatted value for display
                if (tokens > 0) {
                    modal.find('#modal_tokens').val(tokens.toLocaleString('en-US', {maximumFractionDigits: 8}));
                    // Store the raw number value in a hidden field for form submission
                    modal.find('input[name=tokens]').val(tokens.toString());
                } else {
                    modal.find('#modal_tokens').val('0');
                    modal.find('input[name=tokens]').val('0');
                }

                // Update total amount
                modal.find('.modal-final-amount').text('$' + amount.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ","));

                // Enable/disable buy button based on amount
                if (amount < 20) {
                    modal.find('.modal-buy-btn').attr('disabled', true);
                } else {
                    modal.find('.modal-buy-btn').removeAttr('disabled');
                }

                isModalCalculating = false;
            });

            // Calculate amount based on tokens
            modal.find('#modal_tokens').on('input', function() {
                if (isModalCalculating) return;
                isModalCalculating = true;

                // Get tokens value and remove commas
                const tokensDisplay = $(this).val();
                const tokens = parseFloat(tokensDisplay.replace(/,/g, '')) || 0;

                // Store the raw number value in a hidden field for form submission
                modal.find('input[name=tokens]').val(tokens.toString());

                // Calculate amount based on tokens and price
                let amount = 0;
                if (tokenPrice > 0 && tokens > 0) {
                    amount = tokens * tokenPrice;
                }

                // Update amount field
                modal.find('#modal_amount').val(amount.toFixed(2));

                // Update total amount
                modal.find('.modal-final-amount').text('$' + amount.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ","));

                // Enable/disable buy button based on amount
                if (amount < 20) {
                    modal.find('.modal-buy-btn').attr('disabled', true);
                } else {
                    modal.find('.modal-buy-btn').removeAttr('disabled');
                }

                isModalCalculating = false;
            });
        });
    })(jQuery);
</script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('modal'); ?>
<?php echo $__env->make('Template::partials.buy_presale_modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php echo $__env->make('Template::partials.insufficient_funds_modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopPush(); ?>

<?php echo $__env->make($activeTemplate . 'layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\crypto\core\resources\views/templates/dark/user/launchpad/buy.blade.php ENDPATH**/ ?>