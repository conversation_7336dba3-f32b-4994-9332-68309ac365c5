<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-md-12">
        <div class="custom--card">
            <div class="card-header">
                <h5 class="card-title"><?php echo app('translator')->get('Manage Ads'); ?></h5>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="alert alert-info" style="background-color: rgba(49, 215, 169, 0.1); border-color: #31D7A9; color: #B9BABB;">
                            <p class="mb-0"><?php echo app('translator')->get('Manage your active ad banners. Current ad credits: '); ?> <strong><?php echo e(auth()->user()->ad_credits); ?></strong></p>
                        </div>
                    </div>
                </div>

                <?php if($userBanners->count() > 0): ?>
                <div class="row">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <table class="table custom--table">
                                <thead>
                                    <tr>
                                        <th><?php echo app('translator')->get('Position'); ?></th>
                                        <th><?php echo app('translator')->get('Banner'); ?></th>
                                        <th><?php echo app('translator')->get('Status'); ?></th>
                                        <th><?php echo app('translator')->get('Impressions'); ?></th>
                                        <th><?php echo app('translator')->get('Action'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__empty_1 = true; $__currentLoopData = $userBanners; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $banner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td data-label="<?php echo app('translator')->get('Position'); ?>"><?php echo e($banner->adPosition->name); ?></td>
                                        <td data-label="<?php echo app('translator')->get('Banner'); ?>">
                                            <img src="<?php echo e(getImage(getFilePath('ads_images').'/'.$banner->image)); ?>" alt="<?php echo e($banner->adPosition->name); ?>" class="img-thumbnail" style="max-width: 100px;">
                                        </td>
                                        <td data-label="<?php echo app('translator')->get('Status'); ?>">
                                            <?php if($banner->status == 1): ?>
                                                <span class="badge bg-success"><?php echo app('translator')->get('Approved'); ?></span>
                                            <?php elseif($banner->status == 0): ?>
                                                <span class="badge bg-warning"><?php echo app('translator')->get('Pending'); ?></span>
                                            <?php elseif($banner->status == 2): ?>
                                                <span class="badge bg-danger"><?php echo app('translator')->get('Rejected'); ?></span>
                                                <?php if($banner->rejection_reason): ?>
                                                <div class="mt-2">
                                                    <small class="text-danger"><?php echo app('translator')->get('Reason'); ?>: <?php echo e($banner->rejection_reason); ?></small>
                                                </div>
                                                <?php endif; ?>
                                            <?php elseif($banner->status == 3): ?>
                                                <span class="badge bg-secondary"><?php echo app('translator')->get('Paused'); ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td data-label="<?php echo app('translator')->get('Impressions'); ?>">
                                            <?php echo e($banner->getTotalImpressions()); ?>

                                        </td>
                                        <td data-label="<?php echo app('translator')->get('Action'); ?>">
                                            <div class="d-flex flex-wrap gap-1">
                                                <?php if(auth()->user()->ad_credits > 0): ?>
                                                <a href="<?php echo e(route('user.ad.banner.upload.form', $banner->adPosition->id)); ?>" class="btn btn-sm btn--base" style="padding: 0.25rem 0.5rem;"><?php echo app('translator')->get('Update'); ?></a>
                                                <?php else: ?>
                                                <button type="button" class="btn btn-sm btn--base" style="padding: 0.25rem 0.5rem;" data-bs-toggle="modal" data-bs-target="#noCreditsModal" data-action="update"><?php echo app('translator')->get('Update'); ?></button>
                                                <?php endif; ?>

                                                <?php if($banner->status == 1 || $banner->status == 3): ?>
                                                    <?php if(auth()->user()->ad_credits > 0 || $banner->status == 1): ?>
                                                    <button type="button" class="btn btn-sm <?php echo e($banner->status == 1 ? 'btn--warning' : 'btn--success'); ?>" data-bs-toggle="modal" data-bs-target="#pauseBannerModal" data-id="<?php echo e($banner->id); ?>" data-status="<?php echo e($banner->status); ?>">
                                                        <?php if($banner->status == 1): ?>
                                                            <?php echo app('translator')->get('Pause'); ?>
                                                        <?php else: ?>
                                                            <?php echo app('translator')->get('Activate'); ?>
                                                        <?php endif; ?>
                                                    </button>
                                                    <?php else: ?>
                                                    <button type="button" class="btn btn-sm btn--success" data-bs-toggle="modal" data-bs-target="#noCreditsModal" data-action="activate"><?php echo app('translator')->get('Activate'); ?></button>
                                                    <?php endif; ?>
                                                <?php endif; ?>

                                                <?php if($banner->status == 3 || $banner->status == 2): ?>
                                                <button type="button" class="btn btn-sm btn--danger" data-bs-toggle="modal" data-bs-target="#deleteBannerModal" data-id="<?php echo e($banner->id); ?>">
                                                    <?php echo app('translator')->get('Delete'); ?>
                                                </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td class="text-muted text-center" colspan="100%"><?php echo e(__($emptyMessage)); ?></td>
                                    </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <?php if($userBanners->hasPages()): ?>
                <div class="mt-4 pagination-container">
                    <?php echo e(paginateLinks($userBanners)); ?>

                </div>
                <?php endif; ?>
                <?php else: ?>
                <div class="row">
                    <div class="col-md-12">
                        <div class="alert alert-warning">
                            <p class="mb-0"><?php echo app('translator')->get('You don\'t have any active banners yet. Go to Ad Banners to create one.'); ?></p>
                            <div class="mt-3">
                                <a href="<?php echo e(route('user.ad.banners')); ?>" class="btn--base"><?php echo app('translator')->get('Create Banner Ad'); ?></a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <div class="row mt-5">
                    <div class="col-md-12">
                        <div class="custom--card ad-credits-card">
                            <div class="card-header">
                                <h5 class="card-title"><?php echo app('translator')->get('Need More Ad Credits?'); ?></h5>
                            </div>
                            <div class="card-body">
                                <p><?php echo app('translator')->get('Purchase ad credits to promote your project or service on our platform.'); ?></p>
                                <div class="mt-3">
                                    <a href="<?php echo e(route('user.plans.buy.ad.credits')); ?>" class="btn--base"><?php echo app('translator')->get('Buy Ad Credits'); ?></a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Pause Banner Modal -->
<div class="modal fade" id="pauseBannerModal" tabindex="-1" aria-labelledby="pauseBannerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content bg-dark">
            <div class="modal-header" style="border-bottom-color: #2d3748;">
                <h5 class="modal-title text-white" id="pauseBannerModalLabel"><?php echo app('translator')->get('Confirm Action'); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="" method="POST" id="pauseBannerForm">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <p class="text-white pause-message"></p>
                </div>
                <div class="modal-footer" style="border-top-color: #2d3748;">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo app('translator')->get('Cancel'); ?></button>
                    <button type="submit" class="btn btn--base pause-btn"><?php echo app('translator')->get('Confirm'); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Banner Modal -->
<div class="modal fade" id="deleteBannerModal" tabindex="-1" aria-labelledby="deleteBannerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content bg-dark">
            <div class="modal-header" style="border-bottom-color: #2d3748;">
                <h5 class="modal-title text-white" id="deleteBannerModalLabel"><?php echo app('translator')->get('Delete Banner'); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="" method="POST" id="deleteBannerForm">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <p class="text-white"><?php echo app('translator')->get('Are you sure you want to delete this banner? This action cannot be undone.'); ?></p>
                </div>
                <div class="modal-footer" style="border-top-color: #2d3748;">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo app('translator')->get('Cancel'); ?></button>
                    <button type="submit" class="btn btn--danger"><?php echo app('translator')->get('Delete'); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- No Ad Credits Modal -->
<div class="modal fade" id="noCreditsModal" tabindex="-1" aria-labelledby="noCreditsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content bg-dark">
            <div class="modal-header" style="border-bottom-color: #2d3748;">
                <h5 class="modal-title text-white" id="noCreditsModalLabel"><?php echo app('translator')->get('Ad Credits Required'); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="text-white no-credits-message"><?php echo app('translator')->get('You need to purchase ad credits to perform this action.'); ?></p>
            </div>
            <div class="modal-footer" style="border-top-color: #2d3748;">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo app('translator')->get('Cancel'); ?></button>
                <a href="<?php echo e(route('user.plans.buy.ad.credits')); ?>" class="btn btn--base"><?php echo app('translator')->get('Buy Ad Credits'); ?></a>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('style'); ?>
<style>
    /* Override the dots before list items in plans */
    .text-list__item::before,
    .text-list__item::after {
        display: none !important;
    }

    /* Add padding for better alignment without the dots */
    .text-list__item {
        padding-left: 0 !important;
    }

    /* Pagination responsive styles */
    /* Hide unnecessary elements */
    .pagination-wrapper .d-flex.justify-content-between.flex-fill,
    .pagination-container .d-flex.justify-content-between.flex-fill {
        display: none !important;
    }

    /* Main container for pagination */
    .pagination-wrapper,
    .pagination-container {
        width: 100%;
        overflow-x: auto;
        padding: 5px 0;
        margin: 10px 0;
        scrollbar-width: thin;
    }

    .pagination-wrapper .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between,
    .pagination-container .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between {
        display: block !important;
        width: 100%;
    }

    /* Left align the "Showing X to Y of Z results" text */
    .pagination-wrapper .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between > div:first-child,
    .pagination-container .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between > div:first-child {
        text-align: left !important;
        margin-bottom: 10px;
    }

    /* Center the pagination buttons */
    .pagination-wrapper .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between > div:last-child,
    .pagination-wrapper nav,
    .pagination-wrapper .pagination,
    .pagination-container .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between > div:last-child,
    .pagination-container nav,
    .pagination-container .pagination,
    .pagination-container nav > ul {
        display: flex;
        justify-content: center !important;
        width: 100%;
    }

    /* Improved pagination styling for all devices */
    .pagination {
        flex-wrap: wrap;
        gap: 5px;
        margin: 0;
        padding: 0;
        justify-content: center;
        max-width: 100%;
    }

    /* Handle large number of pagination items - general fallback */
    .pagination.pagination-sm {
        justify-content: center;
        padding: 5px 0;
    }

    /* Ensure consistent spacing between pagination items */
    .pagination .page-item {
        margin: 3px;
    }

    /* Style for pagination links */
    .pagination .page-item .page-link {
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 36px;
        height: 36px;
        padding: 0 10px;
        border-radius: 4px !important;
        font-size: 14px;
        transition: all 0.3s ease;
    }

    /* Style for forward and back buttons */
    .pagination .page-item:first-child .page-link,
    .pagination .page-item:last-child .page-link,
    .pagination .page-item.previous .page-link,
    .pagination .page-item.next .page-link {
        font-size: 14px;
        padding: 0 12px;
        min-width: 40px; /* Slightly wider for navigation buttons */
    }

    /* Ensure arrow icons are visible */
    .pagination .page-item .page-link span[aria-hidden="true"] {
        display: inline-block;
        line-height: 1;
    }

    /* Style for disabled pagination items */
    .pagination .page-item.disabled .page-link {
        background-color: hsl(0deg 0% 100% / 40%);
        opacity: 0.7;
    }

    /* Responsive adjustments for smaller screens */
    @media (max-width: 575px) {
        .pagination-wrapper,
        .pagination-container {
            padding: 3px 0;
            margin: 8px 0;
        }

        .pagination {
            gap: 3px;
        }

        .pagination .page-item {
            margin: 2px;
        }

        .pagination .page-item .page-link {
            min-width: 32px;
            height: 32px;
            font-size: 13px;
        }

        /* Adjust forward/back buttons on small screens */
        .pagination .page-item:first-child .page-link,
        .pagination .page-item:last-child .page-link,
        .pagination .page-item.previous .page-link,
        .pagination .page-item.next .page-link {
            min-width: 36px;
            padding: 0 10px;
        }
    }

    /* Responsive adjustments for very small screens */
    @media (max-width: 375px) {
        .pagination .page-item .page-link {
            min-width: 30px;
            height: 30px;
            font-size: 12px;
            padding: 0 8px;
        }
    }

    /* Tablet-specific adjustments */
    @media (min-width: 768px) and (max-width: 1024px) {
        .pagination-wrapper,
        .pagination-container {
            padding: 8px 0;
            margin: 12px 0;
        }

        .pagination {
            gap: 6px;
        }

        .pagination .page-item {
            margin: 3px;
        }

        .pagination .page-item .page-link {
            min-width: 38px;
            height: 38px;
            font-size: 15px;
        }

        /* Adjust forward/back buttons on tablets */
        .pagination .page-item:first-child .page-link,
        .pagination .page-item:last-child .page-link,
        .pagination .page-item.previous .page-link,
        .pagination .page-item.next .page-link {
            min-width: 42px;
            padding: 0 12px;
            font-size: 15px;
        }
    }

    /* Base table responsive styles */
    .table-responsive {
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch;
        width: 100%;
    }

    .custom--table {
        min-width: 800px;
        width: 100%;
    }

    /* Button spacing */
    .gap-1 {
        gap: 0.25rem !important;
    }

    /* Modal styling */
    .modal-content.bg-dark {
        background-color: #1e2530 !important;
    }

    .modal-content.bg-dark .btn-close {
        filter: invert(1) grayscale(100%) brightness(200%);
    }

    /* Responsive modal styling */
    @media (max-width: 767px) {
        .modal-dialog {
            margin: 0.5rem;
            max-width: calc(100% - 1rem);
        }

        .modal-content.bg-dark .modal-header,
        .modal-content.bg-dark .modal-footer {
            padding: 0.75rem;
        }

        .modal-content.bg-dark .modal-body {
            padding: 1rem;
        }

        .modal-title {
            font-size: 16px;
        }
    }

    /* Responsive styles for manage-ads card */
    @media (max-width: 1024px) {
        .custom--card .card-body {
            padding: 20px;
        }

        /* Table responsive styles for all devices */
        .table-responsive {
            overflow-x: auto !important;
            -webkit-overflow-scrolling: touch;
            position: relative;
            width: 100%;
            margin-bottom: 15px;
        }

        .custom--table {
            min-width: 800px;
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
        }

        .custom--table thead th,
        .custom--table tbody td {
            padding: 10px 6px;
            font-size: 13px;
            white-space: nowrap;
        }

        .img-thumbnail {
            max-width: 80px;
        }

        /* Add shadow to indicate scrollable content */
        .table-responsive::after {
            content: "";
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            width: 15px;
            background: linear-gradient(to right, transparent, rgba(0, 0, 0, 0.3));
            pointer-events: none;
        }
    }

    /* Tablet specific styles */
    @media (width: 768px) and (height: 1024px),
           (width: 820px) and (height: 1180px),
           (width: 912px) and (height: 1368px),
           (width: 853px) and (height: 1280px),
           (width: 1024px) and (height: 1366px) {
        .custom--card {
            width: 100%;
        }

        /* Ensure table is scrollable on tablets */
        .table-responsive {
            overflow-x: auto !important;
            -webkit-overflow-scrolling: touch;
            width: 100%;
        }

        .table-responsive table {
            min-width: 800px;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 12px;
        }

        .d-flex.flex-wrap.gap-1 {
            display: flex;
            flex-wrap: wrap;
            gap: 5px !important;
        }
    }

    /* Mobile styles */
    @media (max-width: 767px) {
        .custom--card .card-header {
            padding: 15px;
        }

        .custom--card .card-body {
            padding: 15px;
        }

        .alert {
            padding: 10px;
        }

        .row.mb-4 {
            margin-bottom: 15px !important;
        }

        .row.mt-5 {
            margin-top: 30px !important;
        }

        .btn--base {
            padding: 8px 15px;
            font-size: 14px;
        }

        .img-thumbnail {
            max-width: 60px;
        }

        /* Improve button display on small screens */
        .d-flex.flex-wrap.gap-1 {
            display: flex;
            flex-direction: column;
            gap: 5px !important;
        }

        .d-flex.flex-wrap.gap-1 .btn {
            width: 100%;
            margin-bottom: 5px;
        }

        /* Ensure horizontal scrolling for tables on mobile */
        .table-responsive {
            overflow-x: auto !important;
            -webkit-overflow-scrolling: touch;
            width: 100%;
            max-width: 100%;
            margin-bottom: 15px;
        }

        .table-responsive table {
            min-width: 800px;
            width: 100%;
        }

        .table-responsive table th,
        .table-responsive table td {
            white-space: nowrap;
            padding: 8px 10px;
            font-size: 12px;
        }

        /* Ensure buttons are properly sized on mobile */
        .btn-sm {
            padding: 4px 8px;
            font-size: 11px;
        }

        /* Ensure the image thumbnail is properly sized */
        .img-thumbnail {
            max-width: 50px;
        }
    }

    /* Extra small devices */
    @media (max-width: 480px) {
        .custom--card .card-header {
            padding: 12px;
        }

        .custom--card .card-body {
            padding: 12px;
        }

        .card-title {
            font-size: 16px;
        }

        .alert p {
            font-size: 13px;
        }

        .btn--base {
            padding: 6px 12px;
            font-size: 13px;
        }

        /* Ensure table remains scrollable on extra small devices */
        .table-responsive {
            margin: 0 -12px;
            width: calc(100% + 24px);
            padding: 0;
        }

        .table-responsive table th,
        .table-responsive table td {
            padding: 6px 8px;
            font-size: 11px;
        }

        .img-thumbnail {
            max-width: 40px;
        }
    }

    /* Ad credits card specific styles */
    .ad-credits-card {
        width: 100%;
        margin-bottom: 15px;
    }

    @media (max-width: 1024px) {
        .ad-credits-card .card-body p {
            font-size: 14px;
        }
    }

    @media (max-width: 767px) {
        .ad-credits-card {
            margin-top: 20px;
        }

        .ad-credits-card .card-body p {
            font-size: 13px;
        }
    }

    /* Specific tablet device styles for ad credits card */
    @media (width: 768px) and (height: 1024px),
           (width: 820px) and (height: 1180px),
           (width: 912px) and (height: 1368px),
           (width: 853px) and (height: 1280px),
           (width: 1024px) and (height: 1366px) {
        .ad-credits-card {
            width: 100%;
        }

        .ad-credits-card .btn--base {
            padding: 8px 16px;
            font-size: 14px;
        }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('script'); ?>
<script>
    (function($) {
        "use strict";

        // Pause Banner Modal
        $('#pauseBannerModal').on('show.bs.modal', function (e) {
            var button = $(e.relatedTarget);
            var id = button.data('id');
            var status = button.data('status');
            var modal = $(this);

            var action = "<?php echo e(route('user.ad.banner.pause', '')); ?>/" + id;
            var message = status == 1
                ? "<?php echo app('translator')->get('Are you sure you want to pause this banner? It will no longer be displayed on the site until you activate it again.'); ?>"
                : "<?php echo app('translator')->get('Are you sure you want to activate this banner ad? It will be displayed on the site again.'); ?>";
            var btnText = status == 1 ? "<?php echo app('translator')->get('Pause'); ?>" : "<?php echo app('translator')->get('Activate'); ?>";

            modal.find('.pause-message').text(message);
            modal.find('.pause-btn').text(btnText);
            modal.find('#pauseBannerForm').attr('action', action);
        });

        // Delete Banner Modal
        $('#deleteBannerModal').on('show.bs.modal', function (e) {
            var button = $(e.relatedTarget);
            var id = button.data('id');
            var modal = $(this);

            var action = "<?php echo e(route('user.ad.banner.delete', '')); ?>/" + id;
            modal.find('#deleteBannerForm').attr('action', action);
        });

        // No Credits Modal
        $('#noCreditsModal').on('show.bs.modal', function (e) {
            var button = $(e.relatedTarget);
            var action = button.data('action');
            var modal = $(this);

            var message = action === 'update'
                ? "<?php echo app('translator')->get('You need to purchase ad credits to update your banner ad.'); ?>"
                : "<?php echo app('translator')->get('You need to purchase ad credits to activate your banner ad.'); ?>";

            modal.find('.no-credits-message').text(message);
        });
    })(jQuery);
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make($activeTemplate . 'layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\crypto\core\resources\views/templates/dark/user/manage_ads.blade.php ENDPATH**/ ?>