@extends($activeTemplate . 'layouts.frontend')
@section('content')
<div class="dashboard py-100 section-bg">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-xl-10 col-lg-12 col-md-12">
                <div class="dashboard-body">
                    <div class="dashboard-card mb-4">
                        <div class="dashboard-card__body">
                            <div class="row">
                                <div class="col-lg-12 col-md-12">
                                    <div class="ccl-token-content">
                                        <h5 class="mb-3 section-title-indented">About CCL Token</h5>
                                        <div class="about-content">
                                            @php
                                                // First escape the content for security
                                                $safeContent = e($cclToken->about_content);

                                                // Replace spaces with non-breaking spaces to preserve exact spacing
                                                $safeContent = str_replace('  ', '&nbsp;&nbsp;', $safeContent);

                                                // Replace single newlines with <br> but preserve paragraph spacing
                                                $formattedContent = preg_replace('/(?<!\n)\n(?!\n)/', '<br>', $safeContent);

                                                // Replace double newlines with paragraph breaks
                                                $formattedContent = preg_replace('/\n\n+/', '</p><p>', $formattedContent);

                                                // Wrap in paragraph tags if not already
                                                if (!empty($formattedContent) && strpos($formattedContent, '<p>') !== 0) {
                                                    $formattedContent = '<p>' . $formattedContent . '</p>';
                                                }
                                            @endphp
                                            <div class="about-content-text">
                                                {!! $formattedContent !!}
                                            </div>
                                        </div>

                                        <div class="token-info-card mt-4">
                                            <div class="row token-info-row">
                                                <div class="col-lg-4 col-md-4 col-sm-6 mb-4">
                                                    <div class="token-info-item">
                                                        <h6 class="token-info-indented">Token Name</h6>
                                                        <p>{{ $cclToken->token_name }}</p>
                                                    </div>
                                                </div>
                                                <div class="col-lg-4 col-md-4 col-sm-6 mb-4">
                                                    <div class="token-info-item">
                                                        <h6 class="token-info-indented">Token Symbol</h6>
                                                        <p>{{ $cclToken->token_symbol }}</p>
                                                    </div>
                                                </div>
                                                @if($cclToken->whitepaper_url)
                                                <div class="col-lg-4 col-md-4 col-sm-6 mb-4">
                                                    <div class="token-info-item">
                                                        <h6 class="token-info-indented">Whitepaper</h6>
                                                        <p><a href="{{ $cclToken->whitepaper_url }}" target="_blank" class="whitepaper-link">Download</a></p>
                                                    </div>
                                                </div>
                                                @else
                                                <div class="col-lg-4 col-md-4 col-sm-6 mb-4">
                                                    <!-- Empty div to maintain grid alignment when no whitepaper -->
                                                </div>
                                                @endif
                                                <div class="col-lg-4 col-md-4 col-sm-6 mb-4">
                                                    <div class="token-info-item">
                                                        <h6 class="token-info-indented">Total Supply</h6>
                                                        <p>{{ $cclToken->total_supply }}</p>
                                                    </div>
                                                </div>
                                                <div class="col-lg-4 col-md-4 col-sm-6 mb-4">
                                                    <div class="token-info-item">
                                                        <h6 class="token-info-indented">Blockchain</h6>
                                                        <p>{{ $cclToken->blockchain }}</p>
                                                    </div>
                                                </div>
                                                <div class="col-lg-4 col-md-4 col-sm-12 mb-4 d-flex align-items-center justify-content-center">
                                                    <a href="{{ $cclToken->buy_token_url ?? '#' }}" class="btn btn-token" {{ $cclToken->buy_token_url ? 'target="_blank"' : '' }}>Buy Token</a>
                                                </div>
                                            </div>
                                        </div>

                                        <h5 class="mt-5 mb-3 section-title-indented">Token Utility</h5>
                                        <ul class="token-utility-list">
                                            @foreach($cclToken->utility_items as $item)
                                            <li class="utility-item">
                                                <i class="las la-check-circle utility-icon"></i>
                                                <div class="utility-content">
                                                    <h6 class="utility-title">{{ $item['title'] }}</h6>
                                                    <div class="utility-description">
                                                        @php
                                                            // First escape the content for security
                                                            $safeContent = e($item['description']);

                                                            // Replace spaces with non-breaking spaces to preserve exact spacing
                                                            $safeContent = str_replace('  ', '&nbsp;&nbsp;', $safeContent);

                                                            // Replace single newlines with <br> but preserve paragraph spacing
                                                            $formattedContent = preg_replace('/(?<!\n)\n(?!\n)/', '<br>', $safeContent);

                                                            // Replace double newlines with paragraph breaks
                                                            $formattedContent = preg_replace('/\n\n+/', '</p><p>', $formattedContent);

                                                            // Wrap in paragraph tags if not already
                                                            if (!empty($formattedContent) && strpos($formattedContent, '<p>') !== 0) {
                                                                $formattedContent = '<p>' . $formattedContent . '</p>';
                                                            }
                                                        @endphp
                                                        {!! $formattedContent !!}
                                                    </div>
                                                </div>
                                            </li>
                                            @endforeach
                                        </ul>

                                        <h5 class="mt-5 mb-3 section-title-indented">Roadmap</h5>
                                        <div class="roadmap-container mb-1">
                                            <div class="roadmap-timeline">
                                                @foreach($cclToken->roadmap_items as $index => $item)
                                                <div class="roadmap-item {{ $index % 2 == 0 ? 'left' : 'right' }}">
                                                    <div class="roadmap-content">
                                                        <h6 class="roadmap-title"><span class="roadmap-title-indented">{{ $item['title'] }}</span></h6>
                                                        <div class="roadmap-description">
                                                            @php
                                                                // First escape the content for security
                                                                $safeContent = e($item['description']);

                                                                // Replace spaces with non-breaking spaces to preserve exact spacing
                                                                $safeContent = str_replace('  ', '&nbsp;&nbsp;', $safeContent);

                                                                // Replace single newlines with <br> but preserve paragraph spacing
                                                                $formattedContent = preg_replace('/(?<!\n)\n(?!\n)/', '<br>', $safeContent);

                                                                // Replace double newlines with paragraph breaks
                                                                $formattedContent = preg_replace('/\n\n+/', '</p><p>', $formattedContent);

                                                                // Wrap in paragraph tags if not already
                                                                if (!empty($formattedContent) && strpos($formattedContent, '<p>') !== 0) {
                                                                    $formattedContent = '<p>' . $formattedContent . '</p>';
                                                                }
                                                            @endphp
                                                            <div class="roadmap-text-wrapper">
                                                                {!! $formattedContent !!}
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="roadmap-dot"></div>
                                                </div>
                                                @endforeach
                                            </div>
                                        </div>

                                        <h5 class="mt-2 mb-3 section-title-indented">Token Distribution</h5>
                                        <div class="token-distribution-chart mb-4">
                                            <div class="row distribution-row">
                                                <div class="col-lg-6 col-md-6 mb-4">
                                                    <div class="chart-container">
                                                        <canvas id="tokenDistributionChart"></canvas>
                                                    </div>
                                                </div>
                                                <div class="col-lg-6 col-md-6 mb-4">
                                                    <div class="row distribution-items-row">
                                                        @foreach($cclToken->distribution_items as $item)
                                                        <div class="col-lg-6 col-md-6 col-sm-6 mb-3">
                                                            <div class="distribution-item">
                                                                <div class="distribution-label">
                                                                    <span class="distribution-color" style="background-color: {{ $item['color'] }};"></span>
                                                                    <span>{{ $item['label'] }}: {{ $item['percentage'] }}%</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        @endforeach
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row mt-2">
                                                <div class="col-12 d-flex justify-content-end">
                                                    <a href="{{ $cclToken->buy_token_url ?? '#' }}" class="btn btn-token" {{ $cclToken->buy_token_url ? 'target="_blank"' : '' }}>Buy Token</a>
                                                </div>
                                            </div>
                                        </div>

                                        @if(isset($presales) && count($presales) > 0)
                                        @foreach($presales as $presale)
                                        <h5 class="mt-5 mb-3 section-title-indented">{{ $presale->title }}</h5>
                                        <div class="token-info-card mt-4">
                                            <div class="row token-info-row">
                                                <div class="col-lg-2 col-md-4 col-sm-6 mb-4">
                                                    <div class="token-info-item">
                                                        <h6 class="token-info-indented">Start Date</h6>
                                                        <p>{{ $presale->start_date ? $presale->start_date->format('F d, Y') : 'TBD' }}</p>
                                                    </div>
                                                </div>
                                                <div class="col-lg-2 col-md-4 col-sm-6 mb-4">
                                                    <div class="token-info-item">
                                                        <h6 class="token-info-indented">End Date</h6>
                                                        <p>{{ $presale->end_date ? $presale->end_date->format('F d, Y') : 'TBD' }}</p>
                                                    </div>
                                                </div>
                                                <div class="col-lg-2 col-md-4 col-sm-6 mb-4">
                                                    <div class="token-info-item">
                                                        <h6 class="token-info-indented">Quantity</h6>
                                                        <p>{{ $presale->quantity ?? '0' }}</p>
                                                    </div>
                                                </div>
                                                <div class="col-lg-2 col-md-4 col-sm-6 mb-4">
                                                    <div class="token-info-item">
                                                        <h6 class="token-info-indented">Price</h6>
                                                        <p>${{ showAmount($presale->price, 8, true, true, false) }}</p>
                                                    </div>
                                                </div>
                                                <div class="col-lg-2 col-md-4 col-sm-6 mb-4">
                                                    <div class="token-info-item">
                                                        <h6 class="token-info-indented">Next Price</h6>
                                                        <p>{{ $presale->next_price ? '$' . showAmount($presale->next_price, 8, true, true, false) : 'N/A' }}</p>
                                                    </div>
                                                </div>
                                                <div class="col-lg-2 col-md-4 col-sm-6 mb-4">
                                                    <div class="token-info-item">
                                                        <h6 class="token-info-indented">Status</h6>
                                                        <p class="{{ $presale->getStatus() == 'Active' ? 'text-success' : ($presale->getStatus() == 'Completed' ? 'text-success' : 'text-warning') }}">
                                                            {{ $presale->getStatus() }}
                                                        </p>
                                                    </div>
                                                </div>
                                                <div class="col-12">
                                                    <div class="progress-bar-container mb-3">
                                                        <div class="progress">
                                                            <div class="progress-bar bg-success" role="progressbar" style="width: {{ $presale->getProgressPercentage() }}%;" aria-valuenow="{{ $presale->getProgressPercentage() }}" aria-valuemin="0" aria-valuemax="100">{{ number_format($presale->getProgressPercentage(), 0) }}%</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-12 d-flex justify-content-end">
                                                    @if($presale->getStatus() == 'Active')
                                                    <a href="{{ route('user.ccl.token.buy') }}" class="btn btn-token">Buy Token</a>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                        @endforeach
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-xl-10 col-lg-12 col-md-12">
                <div class="crypto-disclaimer mt-4">
                    <p class="disclaimer-text mb-0"><strong>@lang('Disclaimer:')</strong> @lang('Cryptocurrency investments are volatile and high risk. Prices can fluctuate significantly. We do not guarantee any returns and are not responsible for any losses incurred. Please invest only what you can afford to lose.')</p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('script-lib')
<script src="{{ asset('assets/templates/basic/js/chart.js') }}"></script>
@endpush

@push('script')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get the canvas context
        const ctx = document.getElementById('tokenDistributionChart').getContext('2d');

        // Create a bevel plugin for 3D effect
        const bevelPlugin = {
            beforeDraw: function(chart) {
                if (chart.config.options.bevelEnabled !== true) return;

                const ctx = chart.ctx;
                const meta = chart.getDatasetMeta(0);

                meta.data.forEach(function(arc) {
                    const vm = arc._view;

                    // Save the current state
                    ctx.save();

                    // Create a clip path for the current segment
                    ctx.beginPath();
                    ctx.arc(vm.x, vm.y, vm.outerRadius, vm.startAngle, vm.endAngle);
                    ctx.arc(vm.x, vm.y, vm.innerRadius, vm.endAngle, vm.startAngle, true);
                    ctx.closePath();
                    ctx.clip();

                    // Draw highlight (top-left)
                    const gradient = ctx.createLinearGradient(
                        vm.x - vm.outerRadius,
                        vm.y - vm.outerRadius,
                        vm.x + vm.outerRadius / 3,
                        vm.y + vm.outerRadius / 3
                    );
                    gradient.addColorStop(0, 'rgba(255, 255, 255, 0.5)');
                    gradient.addColorStop(0.5, 'rgba(255, 255, 255, 0.1)');
                    gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');

                    ctx.fillStyle = gradient;
                    ctx.fillRect(
                        vm.x - vm.outerRadius,
                        vm.y - vm.outerRadius,
                        vm.outerRadius * 2,
                        vm.outerRadius * 2
                    );

                    // Restore the context
                    ctx.restore();
                });
            }
        };

        // Create a center text plugin
        const centerTextPlugin = {
            afterDraw: function(chart) {
                const ctx = chart.ctx;
                const width = chart.width;
                const height = chart.height;

                ctx.restore();

                // Font settings
                ctx.font = "bold 14px 'Poppins', sans-serif";
                ctx.textBaseline = "middle";
                ctx.textAlign = "center";

                // Text color
                ctx.fillStyle = "#ffffff";

                // Draw the text in the center
                ctx.fillText("Total Supply:", width / 2, height / 2 - 10);
                ctx.fillText("1 000 000 000", width / 2, height / 2 + 10);

                ctx.save();
            }
        };

        // Register the plugins
        Chart.plugins.register(bevelPlugin);
        Chart.plugins.register(centerTextPlugin);

        // Token distribution data
        const distributionData = {
            labels: [
                @foreach($cclToken->distribution_items as $item)
                '{{ $item['label'] }}',
                @endforeach
            ],
            datasets: [{
                data: [
                    @foreach($cclToken->distribution_items as $item)
                    {{ $item['percentage'] }},
                    @endforeach
                ],
                backgroundColor: [
                    @foreach($cclToken->distribution_items as $item)
                    '{{ $item['color'] }}',
                    @endforeach
                ],
                borderWidth: 0,
                hoverOffset: 20, // Increased offset for more dramatic popout
                hoverBorderWidth: 5, // Add border on hover for enhanced popout effect
                hoverBorderColor: [
                    @foreach($cclToken->distribution_items as $item)
                    'rgba(50, 50, 50, 0.7)',
                    @endforeach
                ]
            }]
        };

        // Chart configuration
        const config = {
            type: 'doughnut',
            data: distributionData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutoutPercentage: 60,
                bevelEnabled: true, // Enable our custom bevel plugin
                legend: {
                    display: false
                },
                elements: {
                    arc: {
                        borderWidth: 1,
                        borderColor: 'rgba(0, 0, 0, 0.3)', // Subtle border for depth
                    }
                },
                tooltips: {
                    enabled: true,
                    mode: 'single',
                    callbacks: {
                        label: function(tooltipItem, data) {
                            const dataset = data.datasets[tooltipItem.datasetIndex];
                            const value = dataset.data[tooltipItem.index];
                            const label = data.labels[tooltipItem.index];
                            return label + ': ' + value + '%';
                        }
                    },
                    backgroundColor: 'rgba(0, 0, 0, 0.7)',
                    titleFontFamily: "'Poppins', sans-serif",
                    titleFontSize: 14,
                    titleFontColor: '#fff',
                    bodyFontFamily: "'Poppins', sans-serif",
                    bodyFontSize: 14,
                    bodyFontColor: '#fff',
                    displayColors: false,
                    caretSize: 6,
                    cornerRadius: 4
                },
                hover: {
                    mode: 'nearest',
                    animationDuration: 300,
                    onHover: function(event, elements) {
                        if (elements && elements.length) {
                            // Change cursor to pointer when hovering over chart elements
                            event.target.style.cursor = 'pointer';
                        } else {
                            event.target.style.cursor = 'default';
                        }
                    }
                },
                // Add animation configuration for smoother popout effect
                animation: {
                    duration: 500,
                    easing: 'easeOutQuart',
                    animateScale: true,
                    animateRotate: true
                }
            }
        };

        // Create the chart
        new Chart(ctx, config);
    });
</script>
@endpush

@push('style')
<style>
    .whitepaper-link {
        color: #BE8400;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .whitepaper-link:hover {
        color: #ffb700;
        text-decoration: underline;
    }

    .dashboard-card {
        background-color: rgba(255, 255, 255, 0.05);
        border-radius: 5px;
        overflow: hidden;
    }

    .dashboard-card__header {
        padding: 20px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .dashboard-card__title {
        margin-bottom: 0;
        font-size: 20px;
        color: #fff;
    }

    .dashboard-card__body {
        padding: 20px;
    }

    .ccl-token-content h5 {
        color: #BE8400;
        font-weight: 600;
    }

    .section-title-indented {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        background-color: #243242;
        border-radius: 6px;
        padding: 6px 12px;
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
        font-family: 'Chakra Petch', sans-serif;
        font-weight: 700;
        letter-spacing: 0.5px;
        text-transform: uppercase;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        background: linear-gradient(to bottom, #ffffff, #BE8400);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .section-title-indented:hover {
        transform: translateY(-2px);
        box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.5);
    }

    .about-content {
        margin-bottom: 20px;
        width: 100%;
        display: block;
    }

    .about-content-text {
        width: 100%;
        display: block;
        overflow-wrap: break-word;
        word-wrap: break-word;
        word-break: break-word;
        hyphens: auto;
    }

    .about-content p {
        margin-bottom: 1em;
        line-height: 1.5;
        text-align: left;
        width: 100%;
        word-wrap: break-word;
        word-break: normal;
        display: block;
        max-width: 100%;
    }

    .about-content p:last-child {
        margin-bottom: 0;
    }

    .token-info-card {
        background-color: rgba(255, 255, 255, 0.03);
        border-radius: 5px;
        padding: 20px;
    }

    .token-info-row {
        display: flex;
        flex-wrap: wrap;
        margin-right: -10px;
        margin-left: -10px;
    }

    .token-info-item {
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .token-info-item h6 {
        color: #BE8400;
        margin-bottom: 5px;
        font-size: 16px;
    }

    .token-info-indented {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        background-color: #243242;
        border-radius: 6px;
        padding: 4px 8px;
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
        font-family: 'Chakra Petch', sans-serif;
        font-weight: 700;
        letter-spacing: 0.5px;
        text-transform: uppercase;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        background: linear-gradient(to bottom, #ffffff, #BE8400);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        width: fit-content;
    }

    .token-info-indented:hover {
        transform: translateY(-2px);
        box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.5);
    }

    .token-info-item p {
        font-size: 18px;
        margin-bottom: 0;
        text-align: center;
    }

    /* Progress bar styles */
    .progress-bar-container {
        width: 100%;
        padding: 0 10px;
    }

    .progress {
        height: 12px;
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 10px;
        overflow: hidden;
        box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
    }

    .progress-bar {
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
        font-size: 10px;
        font-weight: 600;
        transition: width 0.6s ease;
        background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
        background-size: 16px 16px;
        animation: progress-bar-stripes 1s linear infinite;
    }

    @keyframes progress-bar-stripes {
        from { background-position: 16px 0; }
        to { background-position: 0 0; }
    }

    /* Buy Token button styling */
    .btn-token {
        background-color: #BE8400;
        color: #ffffff;
        border: none;
        font-weight: 600;
        padding: 10px 15px;
        transition: background-color 0.3s ease;
    }

    .btn-token:hover {
        background-color: #D99A00; /* Lighter than #BE8400 */
        color: #ffffff;
    }

    .token-utility-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .utility-item {
        display: flex;
        margin-bottom: 20px;
        background-color: rgba(255, 255, 255, 0.03);
        padding: 15px;
        border-radius: 5px;
        width: 100%;
    }

    .utility-icon {
        color: #BE8400;
        font-size: 24px;
        margin-right: 15px;
        margin-top: 3px;
        flex-shrink: 0;
    }

    .utility-content {
        flex: 1;
        min-width: 0; /* Ensures text wrapping works properly */
    }

    .utility-title {
        color: #31D7A9;
        margin-bottom: 5px;
        font-size: 16px;
        word-wrap: break-word;
    }

    .utility-description {
        width: 100%;
        display: block;
    }

    .utility-description p {
        margin-bottom: 0.5em;
        line-height: 1.5;
        text-align: left;
        width: 100%;
        word-wrap: break-word;
        word-break: normal;
        display: block;
    }

    .utility-description p:last-child {
        margin-bottom: 0;
    }

    .distribution-item {
        background-color: rgba(255, 255, 255, 0.03);
        padding: 15px;
        border-radius: 5px;
        height: 100%;
        display: flex;
        align-items: center;
    }

    .distribution-label {
        display: flex;
        align-items: center;
        width: 100%;
        word-break: break-word;
    }

    .distribution-color {
        display: inline-block;
        min-width: 20px;
        min-height: 20px;
        border-radius: 3px;
        margin-right: 10px;
        flex-shrink: 0;
    }

    .distribution-row {
        display: flex;
        flex-wrap: wrap;
        margin-right: -10px;
        margin-left: -10px;
    }

    .distribution-items-row {
        display: flex;
        flex-wrap: wrap;
        height: 100%;
    }

    .chart-container {
        position: relative;
        height: 300px;
        width: 100%;
        margin: 0 auto;
        /* Add 3D effect with box shadow */
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        border-radius: 10px;
        padding: 10px;
        background: rgba(255, 255, 255, 0.03);
    }

    /* Roadmap Styles */
    .roadmap-container {
        position: relative;
        padding: 20px 0;
        width: 100%;
        overflow: visible;
    }

    .roadmap-timeline {
        position: relative;
        width: 100%;
        display: block;
        overflow: visible;
    }

    .roadmap-timeline::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: 50%;
        width: 2px;
        background: linear-gradient(to bottom, rgba(49, 215, 169, 0.6), rgba(190, 132, 0, 0.6), rgba(49, 215, 169, 0.6));
        transform: translateX(-50%);
        z-index: 1;
        box-shadow: 0 0 7px 1px rgba(49, 215, 169, 0.3), 0 0 3px 1px rgba(190, 132, 0, 0.3);
        border-radius: 1px;
    }

    .roadmap-item {
        position: relative;
        margin-bottom: 40px;
        width: 50%;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
    }

    .roadmap-item.left {
        left: 0;
        padding-right: 20px;
        text-align: left;
    }

    .roadmap-item.right {
        left: 50%;
        padding-left: 20px;
        text-align: left;
    }


    .roadmap-content {
        background-color: rgba(255, 255, 255, 0.05);
        padding: 20px;
        border-radius: 5px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        position: relative;
        z-index: 2;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        text-align: left;
        width: 100%;
        display: block;
        box-sizing: border-box;
        max-width: 100%;
        overflow: hidden;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }

    .roadmap-content:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    }

    .roadmap-content h6.roadmap-title {
        color: #BE8400;
        margin-bottom: 10px;
        font-size: 16px;
        text-align: left;
        font-weight: 600;
        word-wrap: break-word;
        overflow-wrap: break-word;
        width: 100%;
        max-width: 100%;
        display: block;
    }

    .roadmap-title-indented {
        display: inline-flex;
        align-items: center;
        justify-content: flex-start;
        background-color: transparent;
        border-radius: 6px;
        padding: 5px 10px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
        width: auto;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }

    .roadmap-title-indented:hover {
        transform: translateY(-2px);
    }

    .roadmap-description {
        width: 100%;
        display: block;
        max-width: 100%;
        box-sizing: border-box;
    }

    .roadmap-description p {
        margin-bottom: 0.5em;
        font-size: 14px;
        line-height: 1.5;
        text-align: left;
        width: 100%;
        word-wrap: break-word;
        word-break: normal;
        display: block;
        white-space: normal;
        overflow-wrap: break-word;
    }

    .roadmap-description p:last-child {
        margin-bottom: 0;
    }

    .roadmap-text-wrapper {
        width: 100%;
        display: block;
        max-width: 100%;
        box-sizing: border-box;
    }

    .roadmap-dot {
        position: absolute;
        width: 20px;
        height: 20px;
        background-color: #31D7A9;
        border-radius: 50%;
        top: 50%;
        transform: translateY(-50%);
        z-index: 2;
        box-shadow: 0 0 0 3px rgba(49, 215, 169, 0.3), 0 0 7px 1px rgba(49, 215, 169, 0.3);
    }

    .roadmap-item.left .roadmap-dot {
        right: -10px;
    }

    .roadmap-item.right .roadmap-dot {
        left: -10px;
    }

    /* Responsive Roadmap for Tablets */
    @media (min-width: 768px) and (max-width: 1024px) {
        .roadmap-timeline::before {
            left: 50%;
            box-shadow: 0 0 7px 1px rgba(49, 215, 169, 0.3), 0 0 3px 1px rgba(190, 132, 0, 0.3);
        }

        .roadmap-item {
            width: 50%;
            margin-bottom: 30px;
            position: relative;
        }

        .roadmap-item.left {
            left: 0;
            padding-right: 25px;
            float: none;
        }

        .roadmap-item.right {
            left: 50%;
            padding-left: 25px;
            float: none;
        }

        .roadmap-content {
            padding: 15px;
            width: 100%;
            box-sizing: border-box;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .roadmap-item.left .roadmap-dot {
            right: -10px;
        }

        .roadmap-item.right .roadmap-dot {
            left: -10px;
        }

        /* Ensure roadmap content doesn't overflow on tablets */
        .roadmap-description {
            font-size: 14px;
        }

        .roadmap-title-indented {
            word-wrap: break-word;
            overflow-wrap: break-word;
        }
    }

    /* Responsive Roadmap for Small Tablets and Large Phones */
    @media (min-width: 576px) and (max-width: 767px) {
        .roadmap-timeline::before {
            left: 20px;
            box-shadow: 0 0 7px 1px rgba(49, 215, 169, 0.3), 0 0 3px 1px rgba(190, 132, 0, 0.3);
        }

        .roadmap-container {
            padding: 15px 0;
            overflow: visible;
        }

        .roadmap-timeline {
            overflow: visible;
        }

        .roadmap-item {
            width: 100%;
            max-width: calc(100% - 20px);
            padding-left: 30px;
            padding-right: 10px;
            text-align: left;
            left: 0;
            float: none;
            box-sizing: border-box;
            margin-bottom: 25px;
        }

        .roadmap-item.left {
            padding-right: 10px;
            padding-left: 30px;
            text-align: left;
            float: none;
            width: 100%;
            max-width: calc(100% - 20px);
            left: 0;
        }

        .roadmap-item.right {
            padding-right: 10px;
            padding-left: 30px;
            text-align: left;
            float: none;
            left: 0;
            width: 100%;
            max-width: calc(100% - 20px);
        }

        .roadmap-content {
            padding: 15px;
            max-width: 100%;
            box-sizing: border-box;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .roadmap-item.left .roadmap-dot {
            left: 10px;
            right: auto;
            top: 50%;
            transform: translateY(-50%);
        }

        .roadmap-item.right .roadmap-dot {
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
        }

        .roadmap-description {
            font-size: 14px;
            max-width: 100%;
        }

        .roadmap-title-indented {
            width: auto;
            word-wrap: break-word;
            overflow-wrap: break-word;
            font-size: 15px;
            justify-content: flex-start;
        }
    }

    /* Responsive Roadmap for Mobile */
    @media (max-width: 575px) {
        .roadmap-timeline::before {
            left: 20px;
            box-shadow: 0 0 7px 1px rgba(49, 215, 169, 0.3), 0 0 3px 1px rgba(190, 132, 0, 0.3);
        }

        .roadmap-container {
            padding: 15px 0;
            overflow: visible;
        }

        .roadmap-timeline {
            overflow: visible;
        }

        .roadmap-item {
            width: 100%;
            max-width: 100%;
            padding-left: 30px;
            padding-right: 10px;
            text-align: left;
            left: 0;
            float: none;
            box-sizing: border-box;
            margin-bottom: 25px;
        }

        .roadmap-item.left {
            padding-right: 10px;
            padding-left: 30px;
            text-align: left;
            float: none;
            width: 100%;
            max-width: calc(100% - 10px);
            left: 0;
        }

        .roadmap-item.right {
            padding-right: 10px;
            padding-left: 30px;
            text-align: left;
            float: none;
            left: 0;
            width: 100%;
            max-width: calc(100% - 10px);
        }

        .roadmap-content {
            padding: 12px;
            max-width: 100%;
            box-sizing: border-box;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .roadmap-item.left .roadmap-dot {
            left: 10px;
            right: auto;
            top: 50%;
            transform: translateY(-50%);
        }

        .roadmap-item.right .roadmap-dot {
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
        }

        .roadmap-description {
            font-size: 13px;
            max-width: 100%;
        }

        .roadmap-title-indented {
            width: auto;
            word-wrap: break-word;
            overflow-wrap: break-word;
            font-size: 14px;
            justify-content: flex-start;
        }
    }

    /* Tablet specific styles */
    @media (min-width: 768px) and (max-width: 1024px) {
        .dashboard-card__header {
            padding: 18px;
        }

        .dashboard-card__body {
            padding: 18px;
        }

        .section-title-indented {
            font-size: 1.4rem;
            padding: 6px 12px;
        }

        .token-info-item h6 {
            font-size: 15px;
        }

        .token-info-indented {
            font-size: 14px;
            padding: 4px 8px;
            width: fit-content;
            justify-content: center;
        }

        .token-info-item p {
            font-size: 17px;
            text-align: center;
        }

        /* Improve token info layout on tablets */
        .token-info-card .row {
            display: flex;
            flex-wrap: wrap;
        }

        .token-info-card .col-md-5 {
            flex: 0 0 50%;
            max-width: 50%;
        }

        /* Adjust Buy Token button for tablets */
        .token-info-card .col-md-2 {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 10px;
        }

        .btn-token {
            padding: 8px 12px;
            font-size: 14px;
        }

        /* Adjust utility list for tablets */
        .utility-item {
            padding: 15px;
            margin-bottom: 15px;
        }

        .utility-icon {
            font-size: 22px;
            margin-right: 12px;
        }

        .utility-title {
            font-size: 15px;
            margin-bottom: 8px;
        }

        .utility-description p {
            font-size: 14px;
        }

        /* Improve chart display on tablets */
        .chart-container {
            height: 280px;
            margin-bottom: 20px;
            padding: 10px;
        }

        /* Adjust distribution items on tablets */
        .distribution-item {
            padding: 12px;
            margin-bottom: 10px;
        }

        .distribution-label {
            font-size: 14px;
        }

        /* Adjust distribution button on tablets */
        .token-distribution-chart .btn-token {
            padding: 8px 12px;
            font-size: 14px;
        }

        /* Improve roadmap display on tablets */
        .roadmap-content {
            padding: 15px;
        }

        .roadmap-title-indented {
            padding: 5px 10px;
            font-size: 15px;
            width: auto;
            justify-content: flex-start;
            display: inline-flex;
        }

        .roadmap-description p {
            font-size: 14px;
        }

        /* Ensure proper spacing between roadmap items */
        .roadmap-item {
            margin-bottom: 30px;
        }
    }

    /* Large tablet styles (iPad Pro, etc.) */
    @media (min-width: 1025px) and (max-width: 1199px) {
        .dashboard-card__header {
            padding: 20px;
        }

        .dashboard-card__body {
            padding: 20px;
        }

        /* Adjust token info for large tablets */
        .token-info-item h6 {
            font-size: 16px;
        }

        .token-info-item p {
            font-size: 18px;
        }

        /* Adjust chart for large tablets */
        .chart-container {
            height: 320px;
        }

        /* Adjust distribution items for large tablets */
        .distribution-item {
            padding: 15px;
        }

        .distribution-label {
            font-size: 15px;
        }

        /* Adjust utility list for large tablets */
        .utility-item {
            padding: 16px;
            margin-bottom: 18px;
        }

        .utility-icon {
            font-size: 23px;
            margin-right: 14px;
        }

        .utility-title {
            font-size: 16px;
            margin-bottom: 8px;
        }

        .utility-description p {
            font-size: 15px;
        }

        /* Adjust roadmap for large tablets */
        .roadmap-item {
            width: 50%;
            position: relative;
            margin-bottom: 35px;
        }

        .roadmap-item.left {
            padding-right: 25px;
            float: none;
        }

        .roadmap-item.right {
            padding-left: 25px;
            float: none;
        }

        .roadmap-content {
            padding: 18px;
            width: 100%;
            box-sizing: border-box;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .roadmap-title-indented {
            font-size: 16px;
            width: auto;
            word-wrap: break-word;
            overflow-wrap: break-word;
            justify-content: flex-start;
            display: inline-flex;
        }

        .roadmap-description p {
            font-size: 15px;
        }
    }

    /* Crypto disclaimer styles */
    .crypto-disclaimer {
        background-color: rgba(190, 132, 0, 0.1);
        border: 1px solid rgba(190, 132, 0, 0.3);
        border-radius: 5px;
        padding: 15px 20px;
        margin-bottom: 20px;
    }

    .disclaimer-text {
        color: #d9edf7;
        font-size: 14px;
        line-height: 1.5;
        text-align: center;
    }

    .disclaimer-text strong {
        color: #BE8400;
    }

    /* Mobile styles */
    @media (max-width: 767px) {
        .dashboard-card__header {
            padding: 15px;
        }

        .dashboard-card__body {
            padding: 15px;
        }

        .section-title-indented {
            font-size: 1.2rem;
            padding: 5px 10px;
        }

        /* Crypto disclaimer mobile adjustments */
        .crypto-disclaimer {
            padding: 12px 15px;
            margin-top: 20px;
        }
        .disclaimer-text {
            font-size: 13px;
        }

        .token-info-item h6 {
            font-size: 14px;
            color: #BE8400;
        }

        .token-info-indented {
            font-size: 13px;
            padding: 3px 6px;
            width: fit-content;
            justify-content: center;
        }

        .roadmap-title-indented {
            padding: 4px 8px;
            width: auto;
            justify-content: flex-start;
            display: inline-flex;
        }

        .token-info-item p {
            font-size: 16px;
            text-align: center;
        }

        .utility-item {
            padding: 12px;
            margin-bottom: 15px;
        }

        .utility-icon {
            font-size: 20px;
            margin-right: 10px;
        }

        .utility-title {
            font-size: 14px;
            margin-bottom: 6px;
        }

        .utility-description p {
            font-size: 13px;
            line-height: 1.4;
        }

        .chart-container {
            height: 250px;
            margin-bottom: 20px;
            padding: 8px;
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
        }

        /* Adjust Buy Token button for mobile */
        .token-info-card .col-sm-12 {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 5px;
            margin-bottom: 15px;
        }

        .btn-token {
            padding: 8px 15px;
            font-size: 14px;
            width: 100%;
            text-align: center;
        }

        /* Adjust distribution button for mobile */
        .token-distribution-chart .row .col-12 {
            justify-content: center;
            margin-top: 10px;
        }

        .token-distribution-chart .btn-token {
            width: 100%;
            max-width: 200px;
        }

        /* Small mobile disclaimer adjustments */
        @media (max-width: 480px) {
            .disclaimer-text {
                font-size: 12px;
            }
        }
    }
</style>
@endpush
