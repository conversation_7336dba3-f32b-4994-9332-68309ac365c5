<!-- Modal -->
<div class="modal custom--modal fade" id="buyPresaleModal" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content section-bg">
            <div class="modal-header">
                <h5 class="modal-title"><?php echo app('translator')->get('Buy Tokens'); ?></h5>
                <button class="close" data-bs-dismiss="modal" type="button" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form action="<?php echo e(route('user.ccl.token.purchase')); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" name="presale_id" id="presale_id">
                    <input type="hidden" name="token_price" id="token_price">
                    <input type="hidden" name="payment_method" value="1">
                    <input type="hidden" name="tokens" value="0">

                    <div class="token-purchase-form-inner">
                        <div class="deposit-info mb-2">
                            <div class="deposit-info__title">
                                <p class="text mb-0"><?php echo app('translator')->get('You Spend (USD)'); ?><span class="text-danger">*</span></p>
                            </div>
                            <div class="deposit-info__input">
                                <div class="input-group amount-input-group">
                                    <input type="number" class="form-control modal-amount" id="modal_amount" name="amount" placeholder="$20*" min="20" step="0.01" required>
                                    <span class="input-group-text">USD</span>
                                </div>
                                <small class="text-danger">*Minimum spend $20</small>
                            </div>
                        </div>

                        <div class="deposit-info mb-2">
                            <div class="deposit-info__title">
                                <p class="text mb-0"><?php echo app('translator')->get('You Receive'); ?></p>
                            </div>
                            <div class="deposit-info__input">
                                <div class="input-group amount-input-group">
                                    <input type="text" class="form-control" id="modal_tokens" name="tokens" placeholder="0">
                                    <span class="input-group-text modal-token-symbol"></span>
                                </div>
                            </div>
                        </div>

                        <div class="deposit-info total-amount">
                            <div class="deposit-info__title">
                                <p class="text mb-0"><?php echo app('translator')->get('Total Amount'); ?></p>
                            </div>
                            <div class="deposit-info__input">
                                <p class="text mb-0 modal-final-amount">$0.00</p>
                            </div>
                        </div>

                        <div class="deposit-info mb-2">
                            <div class="deposit-info__title">
                                <p class="text mb-0"><?php echo app('translator')->get('Your Balance'); ?></p>
                            </div>
                            <div class="deposit-info__input">
                                <p class="text mb-0"><?php echo e(showAmount(auth()->user()->balance)); ?> <?php echo e(__(gs('cur_text'))); ?></p>
                            </div>
                        </div>

                        <div class="mt-3 text-end">
                            <button type="submit" class="btn btn--base w-100 modal-buy-btn" disabled><?php echo app('translator')->get('Buy Now'); ?></button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('style'); ?>
    <style>
        .modal-content {
            text-align: unset !important;
        }

        #buyPresaleModal .deposit-info {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            justify-content: space-between;
            margin-bottom: 12px;
        }

        #buyPresaleModal .deposit-info__title {
            max-width: 50%;
            margin-bottom: 0px;
            text-align: left;
        }

        #buyPresaleModal .deposit-info__input {
            max-width: 50%;
            text-align: right;
            width: 100%;
        }

        #buyPresaleModal .amount-input-group {
            max-width: 200px;
            margin-left: auto;
        }

        #buyPresaleModal .form-control {
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: #fff;
        }

        #buyPresaleModal .input-group-text {
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: #fff;
        }

        #buyPresaleModal .total-amount {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding-top: 8px;
            margin-top: 8px;
            font-weight: 700;
        }

        @media (max-width: 991px) {
            #buyPresaleModal .deposit-info__title,
            #buyPresaleModal .deposit-info__input {
                max-width: 100%;
                width: 100%;
                text-align: left;
            }
            #buyPresaleModal .deposit-info__input {
                margin-top: 5px;
                text-align: left;
            }
            #buyPresaleModal .deposit-info {
                flex-direction: column;
                align-items: flex-start;
            }
            #buyPresaleModal .input-group,
            #buyPresaleModal .amount-input-group {
                max-width: 100% !important;
                margin-left: 0;
            }
        }
    </style>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\xampp\htdocs\crypto\core\resources\views/templates/dark/partials/buy_presale_modal.blade.php ENDPATH**/ ?>