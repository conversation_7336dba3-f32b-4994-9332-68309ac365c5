<!-- Insufficient Funds Modal -->
<div class="modal custom--modal fade" id="insufficientFundsModal" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content section-bg">
            <div class="modal-header">
                <h5 class="modal-title"><?php echo app('translator')->get('Insufficient Funds'); ?></h5>
                <button class="close" data-bs-dismiss="modal" type="button" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="insufficient-funds-container">
                    <div class="icon-container">
                        <div class="icon-circle">
                            <i class="las la-wallet"></i>
                        </div>
                    </div>
                    <div class="message-container">
                        <h5><?php echo app('translator')->get('You need to deposit funds first'); ?></h5>
                        <p><?php echo app('translator')->get('Your account balance is insufficient to purchase tokens. Please deposit funds to your account first.'); ?></p>
                    </div>
                    <div class="balance-info-container">
                        <div class="deposit-info mb-2">
                            <div class="deposit-info__title">
                                <p class="text mb-0"><?php echo app('translator')->get('Your Balance'); ?></p>
                            </div>
                            <div class="deposit-info__input">
                                <p class="text mb-0 current-balance"><?php echo e(showAmount(auth()->user()->balance)); ?> <?php echo e(__(gs('cur_text'))); ?></p>
                            </div>
                        </div>
                        <div class="deposit-info mb-2">
                            <div class="deposit-info__title">
                                <p class="text mb-0"><?php echo app('translator')->get('Minimum Required'); ?></p>
                            </div>
                            <div class="deposit-info__input">
                                <p class="text mb-0 min-required-amount">$20.00</p>
                            </div>
                        </div>
                    </div>
                    <div class="action-container">
                        <a href="<?php echo e(route('user.deposit.funds')); ?>" class="btn btn--base deposit-btn">
                            <i class="las la-plus-circle"></i> <?php echo app('translator')->get('Deposit Funds'); ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('style'); ?>
    <style>
        #insufficientFundsModal .modal-content {
            text-align: unset !important;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
        }

        #insufficientFundsModal .modal-header {
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 15px 20px;
        }

        #insufficientFundsModal .modal-body {
            padding: 25px;
        }

        #insufficientFundsModal .insufficient-funds-container {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        #insufficientFundsModal .icon-container {
            margin-bottom: 20px;
        }

        #insufficientFundsModal .icon-circle {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: rgba(190, 132, 0, 0.15);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            border: 1px solid rgba(190, 132, 0, 0.3);
        }

        #insufficientFundsModal .icon-circle i {
            font-size: 40px;
            color: #BE8400;
        }

        #insufficientFundsModal .message-container {
            text-align: center;
            margin-bottom: 20px;
        }

        #insufficientFundsModal .message-container h5 {
            color: #fff;
            font-size: 18px;
            margin-bottom: 10px;
            font-weight: 600;
        }

        #insufficientFundsModal .message-container p {
            color: #7c97bb;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 0;
        }

        #insufficientFundsModal .balance-info-container {
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 15px;
            width: 100%;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        #insufficientFundsModal .deposit-info {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            justify-content: space-between;
            margin-bottom: 12px;
        }

        #insufficientFundsModal .deposit-info:last-child {
            margin-bottom: 0;
        }

        #insufficientFundsModal .deposit-info__title {
            max-width: 50%;
            margin-bottom: 0px;
            text-align: left;
        }

        #insufficientFundsModal .deposit-info__title p {
            color: #7c97bb;
            font-size: 14px;
        }

        #insufficientFundsModal .deposit-info__input {
            max-width: 50%;
            text-align: right;
            width: 100%;
        }

        #insufficientFundsModal .deposit-info__input p {
            color: #fff;
            font-weight: 600;
            font-size: 14px;
        }

        #insufficientFundsModal .current-balance {
            color: #dc3545 !important;
        }

        #insufficientFundsModal .min-required-amount {
            color: #28c76f !important;
        }

        #insufficientFundsModal .action-container {
            width: 100%;
            text-align: center;
        }

        #insufficientFundsModal .deposit-btn {
            padding: 10px 25px;
            font-weight: 600;
            font-size: 15px;
            transition: all 0.3s ease;
            border-radius: 5px;
            background-color: #BE8400;
            border-color: #BE8400;
        }

        #insufficientFundsModal .deposit-btn:hover {
            background-color: #9e6e00;
            border-color: #9e6e00;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(190, 132, 0, 0.3);
        }

        #insufficientFundsModal .deposit-btn i {
            margin-right: 5px;
        }

        @media (max-width: 991px) {
            #insufficientFundsModal .deposit-info__title,
            #insufficientFundsModal .deposit-info__input {
                max-width: 100%;
                width: 100%;
            }

            #insufficientFundsModal .deposit-info__input {
                margin-top: 5px;
                text-align: left;
            }

            #insufficientFundsModal .deposit-info {
                flex-direction: column;
                align-items: flex-start;
            }

            #insufficientFundsModal .icon-circle {
                width: 70px;
                height: 70px;
            }

            #insufficientFundsModal .icon-circle i {
                font-size: 35px;
            }
        }

        @media (max-width: 575px) {
            #insufficientFundsModal .modal-body {
                padding: 20px 15px;
            }

            #insufficientFundsModal .balance-info-container {
                padding: 12px;
            }

            #insufficientFundsModal .message-container h5 {
                font-size: 16px;
            }

            #insufficientFundsModal .message-container p {
                font-size: 13px;
            }
        }
    </style>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\xampp\htdocs\crypto\core\resources\views/templates/dark/partials/insufficient_funds_modal.blade.php ENDPATH**/ ?>