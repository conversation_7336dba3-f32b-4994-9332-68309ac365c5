<?php $__env->startSection('content'); ?>
    <h5><?php echo e(__($pageTitle)); ?></h5>
    <div class="dashboard-table">
        <table class="table--responsive--lg table">
            <thead>
                <tr>
                    <th><?php echo app('translator')->get('User'); ?></th>
                    <th><?php echo app('translator')->get('Amount'); ?></th>
                    <th><?php echo app('translator')->get('Level'); ?></th>
                    <th><?php echo app('translator')->get('Percent'); ?></th>
                    <th><?php echo app('translator')->get('Time'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php $__empty_1 = true; $__currentLoopData = $logs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $k => $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <tr>
                        <td><?php echo e($data->user->username); ?></td>
                        <td>
                            <?php echo e(showAmount($data->amount)); ?>

                        </td>
                        <td>
                            <?php echo e($data->level); ?>

                        </td>
                        <td>
                            <?php echo e(showAmount($data->percent)); ?>%
                        </td>

                        <td>
                            <?php echo e(showDateTime($data->created_at)); ?>

                        </td>
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <tr>
                        <td class="text-muted text-center" colspan="100%"><?php echo e(__($emptyMessage)); ?></td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
        <?php echo e(paginateLinks($logs)); ?>

    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make($activeTemplate . 'layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\crypto\core\resources\views/templates/dark/user/referral/logs.blade.php ENDPATH**/ ?>