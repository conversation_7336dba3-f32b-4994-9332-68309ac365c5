<?php $__env->startSection('content'); ?>
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card custom--card" style="background-color: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.15); border-radius: 5px;">
                <div class="card-header" style="background-color: #BE8400; color: #ffffff; border-radius: 5px 5px 0 0;">
                    <h5 class="card-title mb-0"><?php echo app('translator')->get('My Withdrawals'); ?></h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover custom--table">
                            <thead>
                                <tr>
                                    <th><?php echo app('translator')->get('Time'); ?></th>
                                    <th><?php echo app('translator')->get('Transaction ID'); ?></th>
                                    <th><?php echo app('translator')->get('Amount'); ?></th>
                                    <th><?php echo app('translator')->get('Status'); ?></th>
                                    <th><?php echo app('translator')->get('View Transaction'); ?></th>
                                    <th><?php echo app('translator')->get('View Explorer'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $withdraws; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $withdrawal): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td data-label="<?php echo app('translator')->get('Time'); ?>">
                                            <?php echo e(showDateTime($withdrawal->created_at)); ?>

                                        </td>
                                        <td data-label="<?php echo app('translator')->get('Transaction ID'); ?>"><?php echo e($withdrawal->trx); ?></td>
                                        <td data-label="<?php echo app('translator')->get('Amount'); ?>">
                                            <strong><?php echo e(showAmount($withdrawal->amount, 8, exceptZeros:true, currencyFormat:false)); ?> <?php echo e(strtoupper($withdrawal->currency)); ?></strong>
                                        </td>

                                        <td data-label="<?php echo app('translator')->get('Status'); ?>">
                                            <?php
                                                echo $withdrawal->statusBadge;
                                            ?>
                                        </td>

                                        <td data-label="<?php echo app('translator')->get('View Transaction'); ?>">
                                            <?php if($withdrawal->status == \App\Constants\Status::PAYMENT_SUCCESS): ?>
                                                <button class="btn btn--success detailBtn btn--xsm" data-admin_feedback="<?php echo e($withdrawal->admin_feedback); ?>"><i class="las la-desktop"></i></button>
                                            <?php elseif($withdrawal->status == \App\Constants\Status::PAYMENT_REJECT): ?>
                                                <button class="btn btn--danger detailBtn btn--xsm" data-admin_feedback="<?php echo e($withdrawal->admin_feedback); ?>"><i class="las la-desktop"></i></button>
                                            <?php else: ?>
                                                <button class="btn btn--warning detailBtn btn--xsm" data-admin_feedback="<?php echo e($withdrawal->admin_feedback ?? __('Pending')); ?>"><i class="las la-desktop"></i></button>
                                            <?php endif; ?>
                                        </td>
                                        <td data-label="<?php echo app('translator')->get('View Explorer'); ?>">
                                            <?php
                                                $withdrawInfo = json_decode($withdrawal->withdraw_information);
                                                $transactionUrl = $withdrawInfo->transaction_url ?? null;
                                            ?>

                                            <?php if($withdrawal->status == \App\Constants\Status::PAYMENT_SUCCESS && $transactionUrl): ?>
                                                <a href="<?php echo e($transactionUrl); ?>" target="_blank" class="btn btn--primary btn--xsm" title="<?php echo app('translator')->get('View Explorer'); ?>"><i class="las la-external-link-alt"></i></a>
                                            <?php else: ?>
                                                <span class="text-muted"><?php echo app('translator')->get('N/A'); ?></span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td class="text-center" colspan="6" data-label=""><?php echo e(__($emptyMessage)); ?></td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php if($withdraws->hasPages()): ?>
    <div class="mt-4 pagination-container">
        <?php echo e(paginateLinks($withdraws)); ?>

    </div>
    <?php endif; ?>
</div>

<div class="modal custom--modal fade" id="detailModal" role="dialog" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php echo app('translator')->get('Details'); ?></h5>
                <button class="close" data-bs-dismiss="modal" type="button" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="withdraw-detail"></div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('style'); ?>
<style>
    /* Table styling */
    .table {
        color: #fff;
    }

    .table thead th {
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        font-weight: 500;
    }

    .table tbody td {
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        vertical-align: middle;
    }

    /* Modal styling */
    .custom--modal .modal-content {
        background-color: #2C2F3E;
        border: 1px solid rgba(255, 255, 255, 0.1);
        color: #fff;
    }

    .custom--modal .modal-header {
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        padding: 15px 20px;
    }

    .custom--modal .modal-body {
        padding: 20px;
    }

    .custom--modal .close {
        color: #fff;
        opacity: 0.8;
        background: transparent;
        border: none;
        font-size: 20px;
    }

    .custom--modal .close:hover {
        opacity: 1;
    }

    /* Base table responsive styles */
    .table-responsive {
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch;
        width: 100%;
    }

    /* Responsive styles for withdrawals card */
    @media (max-width: 1024px) {
        .custom--card .card-body {
            padding: 20px;
        }

        /* Table responsive styles for all devices */
        .table-responsive {
            overflow-x: auto !important;
            -webkit-overflow-scrolling: touch;
            position: relative;
            width: 100%;
            margin-bottom: 15px;
        }

        .custom--table {
            min-width: 800px;
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
        }

        .custom--table thead th,
        .custom--table tbody td {
            padding: 10px 6px;
            font-size: 13px;
            white-space: nowrap;
        }
    }

    /* Pagination responsive styles */
    /* Hide unnecessary elements */
    .pagination-wrapper .d-flex.justify-content-between.flex-fill,
    .pagination-container .d-flex.justify-content-between.flex-fill {
        display: none !important;
    }

    /* Main container for pagination */
    .pagination-wrapper,
    .pagination-container {
        width: 100%;
        overflow-x: auto;
        padding: 5px 0;
        margin: 10px 0;
        scrollbar-width: thin;
    }

    .pagination-wrapper .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between,
    .pagination-container .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between {
        display: block !important;
        width: 100%;
    }

    /* Center the pagination buttons */
    .pagination-wrapper .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between > div:last-child,
    .pagination-wrapper nav,
    .pagination-wrapper .pagination,
    .pagination-container .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between > div:last-child,
    .pagination-container nav,
    .pagination-container .pagination,
    .pagination-container nav > ul {
        display: flex;
        justify-content: center !important;
        width: 100%;
    }

    /* Improved pagination styling for all devices */
    .pagination {
        flex-wrap: wrap;
        gap: 5px;
        margin: 0;
        padding: 0;
        justify-content: center;
        max-width: 100%;
    }

    /* Handle large number of pagination items - general fallback */
    .pagination.pagination-sm {
        justify-content: center;
        padding: 5px 0;
    }

    /* Ensure consistent spacing between pagination items */
    .pagination .page-item {
        margin: 3px;
    }

    /* Style for pagination links */
    .pagination .page-item .page-link {
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 36px;
        height: 36px;
        padding: 0 10px;
        border-radius: 4px !important;
        font-size: 14px;
        transition: all 0.3s ease;
    }

    /* Style for forward and back buttons */
    .pagination .page-item:first-child .page-link,
    .pagination .page-item:last-child .page-link,
    .pagination .page-item.previous .page-link,
    .pagination .page-item.next .page-link {
        font-size: 14px;
        padding: 0 12px;
        min-width: 40px; /* Slightly wider for navigation buttons */
    }

    /* Ensure arrow icons are visible */
    .pagination .page-item .page-link span[aria-hidden="true"] {
        display: inline-block;
        line-height: 1;
    }

    /* Style for disabled pagination items */
    .pagination .page-item.disabled .page-link {
        background-color: hsl(0deg 0% 100% / 40%);
        opacity: 0.7;
    }

    /* Tablet specific styles */
    @media (min-width: 768px) and (max-width: 1024px) {
        .custom--card {
            width: 100%;
        }

        /* Ensure table is scrollable on tablets */
        .table-responsive {
            overflow-x: auto !important;
            -webkit-overflow-scrolling: touch;
            width: 100%;
        }

        .table-responsive table {
            min-width: 800px;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 12px;
        }

        .btn--xsm {
            padding: 0.2rem 0.4rem;
            font-size: 11px;
        }

        /* Tablet-specific pagination adjustments */
        .pagination-wrapper,
        .pagination-container {
            padding: 8px 0;
            margin: 12px 0;
        }

        .pagination {
            gap: 6px;
        }

        .pagination .page-item {
            margin: 3px;
        }

        .pagination .page-item .page-link {
            min-width: 38px;
            height: 38px;
            font-size: 15px;
        }

        /* Adjust forward/back buttons on tablets */
        .pagination .page-item:first-child .page-link,
        .pagination .page-item:last-child .page-link,
        .pagination .page-item.previous .page-link,
        .pagination .page-item.next .page-link {
            min-width: 42px;
            padding: 0 12px;
            font-size: 15px;
        }
    }

    /* Mobile styles */
    @media (max-width: 767px) {
        .custom--card .card-header {
            padding: 15px;
        }

        .custom--card .card-body {
            padding: 15px;
        }

        .card-title {
            font-size: 16px;
        }

        .btn--xsm {
            padding: 4px 8px;
            font-size: 12px;
        }

        /* Ensure horizontal scrolling for tables on mobile */
        .table-responsive {
            overflow-x: auto !important;
            -webkit-overflow-scrolling: touch;
            width: 100%;
            max-width: 100%;
            margin-bottom: 15px;
        }

        .table-responsive table {
            min-width: 800px;
            width: 100%;
        }

        .table-responsive table th,
        .table-responsive table td {
            white-space: nowrap;
            padding: 8px 10px;
            font-size: 12px;
        }

        .pagination-container {
            display: flex;
            justify-content: center;
            overflow-x: auto;
            padding: 10px 0;
        }

        /* Modal responsive styles for mobile */
        .custom--modal .modal-header {
            padding: 12px 15px;
        }

        .custom--modal .modal-body {
            padding: 15px;
        }

        .custom--modal .modal-title {
            font-size: 16px;
        }

        .custom--modal .withdraw-detail p {
            font-size: 13px;
        }

        .custom--modal .btn--sm {
            padding: 6px 12px;
            font-size: 13px;
        }
    }

    /* Extra small devices */
    @media (max-width: 480px) {
        .custom--card .card-header {
            padding: 12px;
        }

        .custom--card .card-body {
            padding: 12px;
        }

        .card-title {
            font-size: 15px;
        }

        .btn--xsm {
            padding: 3px 6px;
            font-size: 11px;
        }

        /* Ensure table remains scrollable on extra small devices */
        .table-responsive {
            margin: 0 -12px;
            width: calc(100% + 24px);
            padding: 0;
        }

        .table-responsive table th,
        .table-responsive table td {
            padding: 6px 8px;
            font-size: 11px;
        }

        .pagination-wrapper,
        .pagination-container {
            padding: 3px 0;
            margin: 8px 0;
        }

        .pagination {
            gap: 3px;
        }

        .pagination .page-item {
            margin: 2px;
        }

        .pagination .page-item .page-link {
            min-width: 32px;
            height: 32px;
            font-size: 13px;
        }

        /* Adjust forward/back buttons on small screens */
        .pagination .page-item:first-child .page-link,
        .pagination .page-item:last-child .page-link,
        .pagination .page-item.previous .page-link,
        .pagination .page-item.next .page-link {
            min-width: 36px;
            padding: 0 10px;
        }

        /* Modal responsive styles for extra small devices */
        .custom--modal .modal-header {
            padding: 10px 12px;
        }

        .custom--modal .modal-body {
            padding: 12px;
        }

        .custom--modal .modal-title {
            font-size: 15px;
        }

        .custom--modal .withdraw-detail p {
            font-size: 12px;
        }

        .custom--modal .btn--sm {
            padding: 5px 10px;
            font-size: 12px;
            width: 100%;
            text-align: center;
            margin-top: 10px;
        }
    }

    /* Responsive adjustments for very small screens */
    @media (max-width: 375px) {
        .pagination .page-item .page-link {
            min-width: 30px;
            height: 30px;
            font-size: 12px;
            padding: 0 8px;
        }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('script'); ?>
    <script>
        'use strict';
        (function($) {
            $('.detailBtn').on('click', function() {
                var modal = $('#detailModal');
                var feedback = $(this).data('admin_feedback');

                // Get the transaction URL if available
                var row = $(this).closest('tr');
                var transactionLink = row.find('a[target="_blank"]').attr('href');

                var detailHtml = `<p>${feedback}</p>`;

                if (transactionLink) {
                    detailHtml += `<div class="mt-3">
                        <a href="${transactionLink}" target="_blank" class="btn btn--primary btn--sm">
                            <?php echo app('translator')->get('View Explorer'); ?>
                        </a>
                    </div>`;
                }

                modal.find('.withdraw-detail').html(detailHtml);
                modal.modal('show');
            });
        })(jQuery);
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make($activeTemplate . 'layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\crypto\core\resources\views/templates/dark/user/withdraw/log.blade.php ENDPATH**/ ?>