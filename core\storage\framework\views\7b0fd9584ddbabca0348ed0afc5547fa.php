<?php $__env->startSection('panel'); ?>
    <div class="row">
        <div class="col-lg-12">
            <div class="card b-radius--10">
                <div class="card-header d-flex justify-content-center">
                    <div class="col-md-6">
                        <form action="<?php echo e(route('admin.tokens.trending.index')); ?>" method="GET" class="d-flex">
                            <div class="input-group w-100">
                                <input type="text" name="search" class="form-control" placeholder="Search by token name or contract address" value="<?php echo e($search ?? ''); ?>">
                                <button class="btn btn--primary input-group-text"><i class="la la-search"></i></button>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive--md table-responsive">
                        <table class="table table--light style--two">
                            <thead>
                                <tr>
                                    <th><?php echo app('translator')->get('Rank'); ?></th>
                                    <th><?php echo app('translator')->get('Token'); ?></th>
                                    <th><?php echo app('translator')->get('Chain'); ?></th>
                                    <th><?php echo app('translator')->get('User Votes'); ?></th>
                                    <th><?php echo app('translator')->get('Admin Trend Votes'); ?></th>
                                    <th><?php echo app('translator')->get('Total Votes'); ?></th>
                                    <th><?php echo app('translator')->get('Action'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $tokens; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $token): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td><?php echo e($token->global_rank); ?></td>
                                    <td>
                                        <div class="d-flex flex-column">
                                            <div class="d-flex align-items-center">
                                                <img src="<?php if(isset($token->image_url) && !filter_var($token->image_url, FILTER_VALIDATE_URL) && strpos($token->image_url, 'http') !== 0): ?><?php echo e(asset('assets/images/coin_logos/'.$token->image_url)); ?><?php else: ?><?php echo e($token->image_url ?? asset('assets/images/default.png')); ?><?php endif; ?>" alt="<?php echo e($token->token_symbol); ?>" class="token-icon me-2" width="32" height="32">
                                                <div>
                                                    <span class="fw-bold"><?php echo e($token->token_symbol); ?></span>
                                                    <small class="d-block"><?php echo e(Str::limit($token->token_name, 30)); ?></small>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td><?php echo e(formatBlockchainName($token->chain_id)); ?></td>
                                    <td><?php echo e($token->regular_votes); ?></td>
                                    <td><?php echo e($token->admin_trend_votes); ?></td>
                                    <td><?php echo e($token->vote_count); ?></td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-outline--primary editBtn me-1"
                                            data-token_id="<?php echo e($token->id); ?>"
                                            data-token_name="<?php echo e($token->token_name); ?>"
                                            data-token_symbol="<?php echo e($token->token_symbol); ?>"
                                            data-trend_votes="<?php echo e($token->trend_votes ?? 0); ?>"
                                        >
                                            <i class="las la-fire"></i> <?php echo app('translator')->get('Vote'); ?>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline--success promoteBtn"
                                            data-token_id="<?php echo e($token->id); ?>"
                                            data-token_name="<?php echo e($token->token_name); ?>"
                                            data-token_symbol="<?php echo e($token->token_symbol); ?>"
                                            data-chain_id="<?php echo e($token->chain_id); ?>"
                                            data-token_address="<?php echo e($token->token_address); ?>"
                                        >
                                            <i class="la la-rocket"></i> <?php echo app('translator')->get('Promote'); ?>
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td class="text-muted text-center" colspan="100%"><?php echo e(__($emptyMessage ?? 'No tokens found')); ?></td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                <?php if($tokens->hasPages()): ?>
                <div class="card-footer py-4">
                    <div class="d-flex justify-content-center">
                        <?php echo e(paginateLinks($tokens)); ?>

                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    
    <div id="editModal" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><?php echo app('translator')->get('Add Token Trend Votes'); ?></h5>
                    <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                        <i class="las la-times"></i>
                    </button>
                </div>
                <form action="<?php echo e(route('admin.tokens.trending.update')); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" name="token_id" id="token_id">
                    <div class="modal-body">
                        <div class="form-group">
                            <label><?php echo app('translator')->get('Token'); ?></label>
                            <input type="text" class="form-control" id="token_name" readonly>
                        </div>
                        <div class="form-group">
                            <label><?php echo app('translator')->get('Admin Trend Votes'); ?></label>
                            <input type="number" class="form-control" name="trend_votes" id="trend_votes" min="0" required>
                            <small class="text-muted"><?php echo app('translator')->get('Set the number of admin trend votes for this token. These votes are added to user votes to determine the token\'s position in the trending list.'); ?></small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn--primary w-100"><?php echo app('translator')->get('Save'); ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    
    <div id="promoteModal" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><?php echo app('translator')->get('Promote Token'); ?></h5>
                    <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                        <i class="las la-times"></i>
                    </button>
                </div>
                <form action="<?php echo e(route('admin.tokens.trending.promote')); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" name="token_id" id="promote_token_id">
                    <div class="modal-body">
                        <div class="form-group">
                            <label><?php echo app('translator')->get('Token'); ?></label>
                            <input type="text" class="form-control" id="promote_token_name" readonly>
                        </div>
                        <div class="form-group">
                            <label><?php echo app('translator')->get('Chain'); ?></label>
                            <input type="text" class="form-control" id="promote_chain_id" readonly>
                        </div>
                        <div class="form-group">
                            <label><?php echo app('translator')->get('Promotion Days'); ?></label>
                            <input type="number" class="form-control" name="days" id="promote_days" min="1" max="100" value="1" required>
                            <small class="text-muted"><?php echo app('translator')->get('Set the number of days to promote this token. The token will be moved to the Promoted Coins section for this duration.'); ?></small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn--success w-100"><?php echo app('translator')->get('Promote Token'); ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('breadcrumb-plugins'); ?>
<div class="d-flex flex-wrap justify-content-end">
    <a href="<?php echo e(route('admin.dexscreener.index')); ?>" class="btn btn-sm btn-outline--primary me-2">
        <i class="las la-list"></i> <?php echo app('translator')->get('Dexscreener Tokens'); ?>
    </a>
</div>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('style'); ?>
<style>
    .token-icon {
        border-radius: 50%;
        object-fit: cover;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('script'); ?>
<script>
    (function($) {
        "use strict";

        $('.editBtn').on('click', function() {
            var modal = $('#editModal');
            modal.find('#token_id').val($(this).data('token_id'));
            modal.find('#token_name').val($(this).data('token_name') + ' (' + $(this).data('token_symbol') + ')');
            modal.find('#trend_votes').val($(this).data('trend_votes'));
            modal.modal('show');
        });

        $('.promoteBtn').on('click', function() {
            var modal = $('#promoteModal');
            modal.find('#promote_token_id').val($(this).data('token_id'));
            modal.find('#promote_token_name').val($(this).data('token_name') + ' (' + $(this).data('token_symbol') + ')');
            modal.find('#promote_chain_id').val($(this).data('chain_id'));
            modal.modal('show');
        });
    })(jQuery);
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\crypto\core\resources\views/admin/tokens/trending.blade.php ENDPATH**/ ?>