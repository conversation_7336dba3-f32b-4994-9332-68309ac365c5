[2025-06-13 10:22:49] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(363): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(900): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(787): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(723): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1015): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1058): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(974): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(935): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(787): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#13 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#14 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(723): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#15 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1015): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#16 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(172): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#17 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#21 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laramin\\utility\\src\\Utility.php(11): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Laramin\\Utility\\Utility->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#28 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#30 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laramin\\utility\\src\\Utility.php(11): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Laramin\\Utility\\Utility->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1172): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\crypto\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#54 {main}
"} 
[2025-06-13 10:22:49] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(363): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(900): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(787): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(723): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1015): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1058): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(974): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(935): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(787): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#13 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#14 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(723): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#15 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1015): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#16 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(255): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#17 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(213): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#18 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1174): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#19 C:\\xampp\\htdocs\\crypto\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#20 {main}
"} 
[2025-06-14 21:56:07] production.INFO: Fetching token pairs {"url":"https://api.dexscreener.com/latest/dex/pairs/bsc/presale_684e283007545_2","chainId":"bsc","tokenAddress":"presale_684e283007545_2"} 
[2025-06-14 21:56:10] production.INFO: Successfully fetched token pairs {"count":0} 
[2025-06-14 21:57:39] production.INFO: Fetching token pairs {"url":"https://api.dexscreener.com/latest/dex/pairs/bsc/presale_684e283007545_2","chainId":"bsc","tokenAddress":"presale_684e283007545_2"} 
[2025-06-14 21:57:40] production.INFO: Successfully fetched token pairs {"count":0} 
[2025-06-14 22:00:04] production.ERROR: Database file at path [C:\xampp\htdocs\crypto\core\database\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select count(*) as aggregate from "user_banners" where "id" = 29) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): Database file at path [C:\\xampp\\htdocs\\crypto\\core\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select count(*) as aggregate from \"user_banners\" where \"id\" = 29) at C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:813)
[stacktrace]
#0 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(767): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2993): Illuminate\\Database\\Connection->select('select count(*)...', Array, false)
#3 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2978): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3566): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2977): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3493): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3421): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#8 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php(54): Illuminate\\Database\\Query\\Builder->count()
#9 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php(915): Illuminate\\Validation\\DatabasePresenceVerifier->getCount('user_banners', 'id', '29', NULL, NULL, Array)
#10 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php(886): Illuminate\\Validation\\Validator->getExistCount(NULL, 'user_banners', 'id', '29', Array)
#11 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(664): Illuminate\\Validation\\Validator->validateExists('banner_id', '29', Array, Object(Illuminate\\Validation\\Validator))
#12 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(459): Illuminate\\Validation\\Validator->validateAttribute('banner_id', 'Exists')
#13 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(494): Illuminate\\Validation\\Validator->passes()
#14 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(536): Illuminate\\Validation\\Validator->fails()
#15 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FoundationServiceProvider.php(139): Illuminate\\Validation\\Validator->validate()
#16 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php(123): Illuminate\\Http\\Request->Illuminate\\Foundation\\Providers\\{closure}(Array)
#17 C:\\xampp\\htdocs\\crypto\\core\\app\\Http\\Controllers\\Api\\AdImpressionController.php(23): Illuminate\\Http\\Request->__call('validate', Array)
#18 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\AdImpressionController->recordImpression(Object(Illuminate\\Http\\Request))
#19 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\AdImpressionController), 'recordImpressio...')
#20 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(206): Illuminate\\Routing\\Route->runController()
#21 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#22 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\crypto\\core\\app\\Http\\Middleware\\MaintenanceMode.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\MaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laramin\\utility\\src\\Utility.php(11): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Laramin\\Utility\\Utility->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#33 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laramin\\utility\\src\\Utility.php(11): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Laramin\\Utility\\Utility->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1172): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\crypto\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#57 {main}

[previous exception] [object] (Illuminate\\Database\\SQLiteDatabaseDoesNotExistException(code: 0): Database file at path [C:\\xampp\\htdocs\\crypto\\core\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. at C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php:34)
[stacktrace]
#0 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(222): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#1 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#2 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1219): call_user_func(Object(Closure))
#3 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getPdo()
#4 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(false)
#5 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(800): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select count(*)...', Array)
#6 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(767): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#7 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#8 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2993): Illuminate\\Database\\Connection->select('select count(*)...', Array, false)
#9 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2978): Illuminate\\Database\\Query\\Builder->runSelect()
#10 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3566): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#11 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2977): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#12 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3493): Illuminate\\Database\\Query\\Builder->get(Array)
#13 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3421): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#14 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php(54): Illuminate\\Database\\Query\\Builder->count()
#15 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php(915): Illuminate\\Validation\\DatabasePresenceVerifier->getCount('user_banners', 'id', '29', NULL, NULL, Array)
#16 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php(886): Illuminate\\Validation\\Validator->getExistCount(NULL, 'user_banners', 'id', '29', Array)
#17 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(664): Illuminate\\Validation\\Validator->validateExists('banner_id', '29', Array, Object(Illuminate\\Validation\\Validator))
#18 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(459): Illuminate\\Validation\\Validator->validateAttribute('banner_id', 'Exists')
#19 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(494): Illuminate\\Validation\\Validator->passes()
#20 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(536): Illuminate\\Validation\\Validator->fails()
#21 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FoundationServiceProvider.php(139): Illuminate\\Validation\\Validator->validate()
#22 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php(123): Illuminate\\Http\\Request->Illuminate\\Foundation\\Providers\\{closure}(Array)
#23 C:\\xampp\\htdocs\\crypto\\core\\app\\Http\\Controllers\\Api\\AdImpressionController.php(23): Illuminate\\Http\\Request->__call('validate', Array)
#24 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\AdImpressionController->recordImpression(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\AdImpressionController), 'recordImpressio...')
#26 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(206): Illuminate\\Routing\\Route->runController()
#27 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#28 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\crypto\\core\\app\\Http\\Middleware\\MaintenanceMode.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\MaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laramin\\utility\\src\\Utility.php(11): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Laramin\\Utility\\Utility->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#38 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#39 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laramin\\utility\\src\\Utility.php(11): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Laramin\\Utility\\Utility->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#60 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#61 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1172): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#62 C:\\xampp\\htdocs\\crypto\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#63 {main}
"} 
[2025-06-14 22:35:03] production.ERROR: Database file at path [C:\xampp\htdocs\crypto\core\database\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select count(*) as aggregate from "user_banners" where "id" = 23) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): Database file at path [C:\\xampp\\htdocs\\crypto\\core\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select count(*) as aggregate from \"user_banners\" where \"id\" = 23) at C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:813)
[stacktrace]
#0 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(767): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2993): Illuminate\\Database\\Connection->select('select count(*)...', Array, false)
#3 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2978): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3566): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2977): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3493): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3421): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#8 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php(54): Illuminate\\Database\\Query\\Builder->count()
#9 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php(915): Illuminate\\Validation\\DatabasePresenceVerifier->getCount('user_banners', 'id', '23', NULL, NULL, Array)
#10 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php(886): Illuminate\\Validation\\Validator->getExistCount(NULL, 'user_banners', 'id', '23', Array)
#11 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(664): Illuminate\\Validation\\Validator->validateExists('banner_id', '23', Array, Object(Illuminate\\Validation\\Validator))
#12 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(459): Illuminate\\Validation\\Validator->validateAttribute('banner_id', 'Exists')
#13 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(494): Illuminate\\Validation\\Validator->passes()
#14 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(536): Illuminate\\Validation\\Validator->fails()
#15 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FoundationServiceProvider.php(139): Illuminate\\Validation\\Validator->validate()
#16 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php(123): Illuminate\\Http\\Request->Illuminate\\Foundation\\Providers\\{closure}(Array)
#17 C:\\xampp\\htdocs\\crypto\\core\\app\\Http\\Controllers\\Api\\AdImpressionController.php(23): Illuminate\\Http\\Request->__call('validate', Array)
#18 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\AdImpressionController->recordImpression(Object(Illuminate\\Http\\Request))
#19 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\AdImpressionController), 'recordImpressio...')
#20 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(206): Illuminate\\Routing\\Route->runController()
#21 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#22 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\crypto\\core\\app\\Http\\Middleware\\MaintenanceMode.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\MaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laramin\\utility\\src\\Utility.php(11): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Laramin\\Utility\\Utility->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#33 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laramin\\utility\\src\\Utility.php(11): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Laramin\\Utility\\Utility->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1172): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\crypto\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#57 {main}

[previous exception] [object] (Illuminate\\Database\\SQLiteDatabaseDoesNotExistException(code: 0): Database file at path [C:\\xampp\\htdocs\\crypto\\core\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. at C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php:34)
[stacktrace]
#0 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(222): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#1 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#2 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1219): call_user_func(Object(Closure))
#3 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getPdo()
#4 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(false)
#5 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(800): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select count(*)...', Array)
#6 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(767): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#7 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#8 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2993): Illuminate\\Database\\Connection->select('select count(*)...', Array, false)
#9 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2978): Illuminate\\Database\\Query\\Builder->runSelect()
#10 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3566): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#11 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2977): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#12 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3493): Illuminate\\Database\\Query\\Builder->get(Array)
#13 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3421): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#14 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php(54): Illuminate\\Database\\Query\\Builder->count()
#15 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php(915): Illuminate\\Validation\\DatabasePresenceVerifier->getCount('user_banners', 'id', '23', NULL, NULL, Array)
#16 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php(886): Illuminate\\Validation\\Validator->getExistCount(NULL, 'user_banners', 'id', '23', Array)
#17 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(664): Illuminate\\Validation\\Validator->validateExists('banner_id', '23', Array, Object(Illuminate\\Validation\\Validator))
#18 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(459): Illuminate\\Validation\\Validator->validateAttribute('banner_id', 'Exists')
#19 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(494): Illuminate\\Validation\\Validator->passes()
#20 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(536): Illuminate\\Validation\\Validator->fails()
#21 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FoundationServiceProvider.php(139): Illuminate\\Validation\\Validator->validate()
#22 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php(123): Illuminate\\Http\\Request->Illuminate\\Foundation\\Providers\\{closure}(Array)
#23 C:\\xampp\\htdocs\\crypto\\core\\app\\Http\\Controllers\\Api\\AdImpressionController.php(23): Illuminate\\Http\\Request->__call('validate', Array)
#24 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\AdImpressionController->recordImpression(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\AdImpressionController), 'recordImpressio...')
#26 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(206): Illuminate\\Routing\\Route->runController()
#27 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#28 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\crypto\\core\\app\\Http\\Middleware\\MaintenanceMode.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\MaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laramin\\utility\\src\\Utility.php(11): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Laramin\\Utility\\Utility->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#38 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#39 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laramin\\utility\\src\\Utility.php(11): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Laramin\\Utility\\Utility->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#60 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#61 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1172): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#62 C:\\xampp\\htdocs\\crypto\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#63 {main}
"} 
[2025-06-14 22:35:34] production.ERROR: Database file at path [C:\xampp\htdocs\crypto\core\database\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select count(*) as aggregate from "user_banners" where "id" = 23) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): Database file at path [C:\\xampp\\htdocs\\crypto\\core\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select count(*) as aggregate from \"user_banners\" where \"id\" = 23) at C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:813)
[stacktrace]
#0 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(767): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2993): Illuminate\\Database\\Connection->select('select count(*)...', Array, false)
#3 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2978): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3566): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2977): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3493): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3421): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#8 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php(54): Illuminate\\Database\\Query\\Builder->count()
#9 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php(915): Illuminate\\Validation\\DatabasePresenceVerifier->getCount('user_banners', 'id', '23', NULL, NULL, Array)
#10 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php(886): Illuminate\\Validation\\Validator->getExistCount(NULL, 'user_banners', 'id', '23', Array)
#11 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(664): Illuminate\\Validation\\Validator->validateExists('banner_id', '23', Array, Object(Illuminate\\Validation\\Validator))
#12 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(459): Illuminate\\Validation\\Validator->validateAttribute('banner_id', 'Exists')
#13 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(494): Illuminate\\Validation\\Validator->passes()
#14 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(536): Illuminate\\Validation\\Validator->fails()
#15 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FoundationServiceProvider.php(139): Illuminate\\Validation\\Validator->validate()
#16 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php(123): Illuminate\\Http\\Request->Illuminate\\Foundation\\Providers\\{closure}(Array)
#17 C:\\xampp\\htdocs\\crypto\\core\\app\\Http\\Controllers\\Api\\AdImpressionController.php(23): Illuminate\\Http\\Request->__call('validate', Array)
#18 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\AdImpressionController->recordImpression(Object(Illuminate\\Http\\Request))
#19 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\AdImpressionController), 'recordImpressio...')
#20 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(206): Illuminate\\Routing\\Route->runController()
#21 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#22 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\crypto\\core\\app\\Http\\Middleware\\MaintenanceMode.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\MaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laramin\\utility\\src\\Utility.php(11): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Laramin\\Utility\\Utility->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#33 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laramin\\utility\\src\\Utility.php(11): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Laramin\\Utility\\Utility->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1172): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\crypto\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#57 {main}

[previous exception] [object] (Illuminate\\Database\\SQLiteDatabaseDoesNotExistException(code: 0): Database file at path [C:\\xampp\\htdocs\\crypto\\core\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. at C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php:34)
[stacktrace]
#0 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(222): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#1 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#2 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1219): call_user_func(Object(Closure))
#3 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getPdo()
#4 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(false)
#5 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(800): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select count(*)...', Array)
#6 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(767): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#7 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#8 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2993): Illuminate\\Database\\Connection->select('select count(*)...', Array, false)
#9 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2978): Illuminate\\Database\\Query\\Builder->runSelect()
#10 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3566): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#11 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2977): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#12 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3493): Illuminate\\Database\\Query\\Builder->get(Array)
#13 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3421): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#14 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php(54): Illuminate\\Database\\Query\\Builder->count()
#15 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php(915): Illuminate\\Validation\\DatabasePresenceVerifier->getCount('user_banners', 'id', '23', NULL, NULL, Array)
#16 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php(886): Illuminate\\Validation\\Validator->getExistCount(NULL, 'user_banners', 'id', '23', Array)
#17 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(664): Illuminate\\Validation\\Validator->validateExists('banner_id', '23', Array, Object(Illuminate\\Validation\\Validator))
#18 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(459): Illuminate\\Validation\\Validator->validateAttribute('banner_id', 'Exists')
#19 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(494): Illuminate\\Validation\\Validator->passes()
#20 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(536): Illuminate\\Validation\\Validator->fails()
#21 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FoundationServiceProvider.php(139): Illuminate\\Validation\\Validator->validate()
#22 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php(123): Illuminate\\Http\\Request->Illuminate\\Foundation\\Providers\\{closure}(Array)
#23 C:\\xampp\\htdocs\\crypto\\core\\app\\Http\\Controllers\\Api\\AdImpressionController.php(23): Illuminate\\Http\\Request->__call('validate', Array)
#24 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\AdImpressionController->recordImpression(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\AdImpressionController), 'recordImpressio...')
#26 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(206): Illuminate\\Routing\\Route->runController()
#27 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#28 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\crypto\\core\\app\\Http\\Middleware\\MaintenanceMode.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\MaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laramin\\utility\\src\\Utility.php(11): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Laramin\\Utility\\Utility->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#38 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#39 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laramin\\utility\\src\\Utility.php(11): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Laramin\\Utility\\Utility->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#60 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#61 C:\\xampp\\htdocs\\crypto\\core\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1172): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#62 C:\\xampp\\htdocs\\crypto\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#63 {main}
"} 
