<?php

namespace App\Http\Controllers\Admin\Auth;

use App\Constants\Status;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AuthorizationController extends Controller
{
    public function __construct()
    {
        $this->middleware('admin');
    }

    public function checkValidCode($user, $code, $add_min = 0)
    {
        if (!$code) return false;
        
        if (!$user->tv) {
            $user->tv = Status::YES;
            $user->save();
            return true;
        }
        
        return verifyG2fa($user, $code);
    }

    public function show2faForm()
    {
        $pageTitle = '2FA Verification';
        return view('admin.auth.authorization.2fa', compact('pageTitle'));
    }

    public function verify2fa(Request $request)
    {
        $request->validate([
            'code' => 'required',
        ]);
        
        $user = auth('admin')->user();
        
        if ($this->checkValidCode($user, $request->code)) {
            $user->tv = Status::YES;
            $user->save();
            
            return to_route('admin.dashboard');
        }
        
        $notify[] = ['error', 'Wrong verification code'];
        return back()->withNotify($notify);
    }
}
